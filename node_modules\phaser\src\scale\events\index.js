/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.Scale.Events
 */

module.exports = {

    ENTER_FULLSCREEN: require('./ENTER_FULLSCREEN_EVENT'),
    FULLSCREEN_FAILED: require('./FULLSCREEN_FAILED_EVENT'),
    FULLSCREEN_UNSUPPORTED: require('./FULLSCREEN_UNSUPPORTED_EVENT'),
    LEAVE_FULLSCREEN: require('./LEAVE_FULLSCREEN_EVENT'),
    ORIENTATION_CHANGE: require('./ORIENTATION_CHANGE_EVENT'),
    RESIZE: require('./RESIZE_EVENT')

};
