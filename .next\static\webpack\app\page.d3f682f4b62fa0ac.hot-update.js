"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/constants.js":
/*!******************************!*\
  !*** ./src/lib/constants.js ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATIONS: () => (/* binding */ ANIMATIONS),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   COLORS: () => (/* binding */ COLORS),\n/* harmony export */   DEMO_CATEGORIES: () => (/* binding */ DEMO_CATEGORIES),\n/* harmony export */   FEATURES: () => (/* binding */ FEATURES),\n/* harmony export */   FONTS: () => (/* binding */ FONTS),\n/* harmony export */   FX_POOL: () => (/* binding */ FX_POOL),\n/* harmony export */   GRADIENTS: () => (/* binding */ GRADIENTS),\n/* harmony export */   NAVIGATION: () => (/* binding */ NAVIGATION),\n/* harmony export */   PRICING_PLANS: () => (/* binding */ PRICING_PLANS),\n/* harmony export */   STATS: () => (/* binding */ STATS),\n/* harmony export */   TESTIMONIALS: () => (/* binding */ TESTIMONIALS),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// WarrantyAI - Application Constants\nconst APP_CONFIG = {\n    name: \"WarrantyAI\",\n    tagline: \"Never miss a warranty again\",\n    description: \"Smart AI assistant for tracking warranties, services, and coverage\",\n    url: \"https://warrantyai.com\",\n    email: \"<EMAIL>\",\n    version: \"1.0.0\"\n};\nconst COLORS = {\n    primary: {\n        blue: \"#0066FF\",\n        cyan: \"#00FFFF\",\n        purple: \"#6366F1\",\n        pink: \"#EC4899\"\n    },\n    accent: {\n        green: \"#10B981\",\n        orange: \"#F59E0B\",\n        red: \"#EF4444\"\n    },\n    neutral: {\n        900: \"#0F0F23\",\n        800: \"#1A1A2E\",\n        700: \"#16213E\",\n        600: \"#0F3460\",\n        500: \"#533A7B\",\n        400: \"#9CA3AF\",\n        300: \"#D1D5DB\",\n        200: \"#E5E7EB\",\n        100: \"#F3F4F6\",\n        50: \"#F9FAFB\"\n    }\n};\nconst GRADIENTS = {\n    primary: \"linear-gradient(135deg, #0066FF, #00FFFF)\",\n    secondary: \"linear-gradient(135deg, #6366F1, #EC4899)\",\n    accent: \"linear-gradient(135deg, #10B981, #F59E0B)\",\n    dark: \"linear-gradient(135deg, #0F0F23, #1A1A2E)\"\n};\nconst FONTS = {\n    primary: \"Inter, system-ui, sans-serif\",\n    display: \"Space Grotesk, system-ui, sans-serif\",\n    mono: \"JetBrains Mono, monospace\"\n};\nconst ANIMATIONS = {\n    duration: {\n        fast: \"0.15s\",\n        normal: \"0.3s\",\n        slow: \"0.5s\",\n        slower: \"0.75s\"\n    },\n    easing: {\n        default: \"ease\",\n        in: \"ease-in\",\n        out: \"ease-out\",\n        inOut: \"ease-in-out\"\n    }\n};\n// FX Pool - Available effects for sections\nconst FX_POOL = [\n    \"parallax-scroll\",\n    \"scroll-triggered\",\n    \"multi-layer-depth\",\n    \"3d-tilt-hover\",\n    \"3d-product-viewer\",\n    \"floating-elements\",\n    \"ai-eye-tracker\",\n    \"ai-response-bubble\",\n    \"matrix-effect\",\n    \"multi-cursor\",\n    \"ghost-cursors\",\n    \"typing-text\",\n    \"infinite-zoom\",\n    \"morphing-shapes\",\n    \"animated-svgs\",\n    \"terminal-typing\",\n    \"scroll-morphing\",\n    \"audio-responsive\",\n    \"carousel-3d\",\n    \"mini-demo-animation\"\n];\n// Navigation Links\nconst NAVIGATION = {\n    main: [\n        {\n            name: \"Home\",\n            href: \"/\",\n            active: true\n        },\n        {\n            name: \"Demo\",\n            href: \"/demo\"\n        },\n        {\n            name: \"Features\",\n            href: \"/features\"\n        },\n        {\n            name: \"Pricing\",\n            href: \"/pricing\"\n        },\n        {\n            name: \"About\",\n            href: \"/about\"\n        }\n    ],\n    footer: [\n        {\n            title: \"Product\",\n            links: [\n                {\n                    name: \"Features\",\n                    href: \"/features\"\n                },\n                {\n                    name: \"Demo\",\n                    href: \"/demo\"\n                },\n                {\n                    name: \"Pricing\",\n                    href: \"/pricing\"\n                },\n                {\n                    name: \"Roadmap\",\n                    href: \"/roadmap\"\n                }\n            ]\n        },\n        {\n            title: \"Company\",\n            links: [\n                {\n                    name: \"About\",\n                    href: \"/about\"\n                },\n                {\n                    name: \"Blog\",\n                    href: \"/blog\"\n                },\n                {\n                    name: \"Careers\",\n                    href: \"/careers\"\n                },\n                {\n                    name: \"Contact\",\n                    href: \"/contact\"\n                }\n            ]\n        },\n        {\n            title: \"Support\",\n            links: [\n                {\n                    name: \"Help Center\",\n                    href: \"/help\"\n                },\n                {\n                    name: \"Documentation\",\n                    href: \"/docs\"\n                },\n                {\n                    name: \"API\",\n                    href: \"/api\"\n                },\n                {\n                    name: \"Status\",\n                    href: \"/status\"\n                }\n            ]\n        },\n        {\n            title: \"Legal\",\n            links: [\n                {\n                    name: \"Privacy\",\n                    href: \"/privacy\"\n                },\n                {\n                    name: \"Terms\",\n                    href: \"/terms\"\n                },\n                {\n                    name: \"Security\",\n                    href: \"/security\"\n                },\n                {\n                    name: \"Cookies\",\n                    href: \"/cookies\"\n                }\n            ]\n        }\n    ]\n};\n// Feature Categories\nconst FEATURES = {\n    core: [\n        {\n            id: \"ai-extraction\",\n            name: \"AI Receipt Extraction\",\n            description: \"Automatically extract warranty info from receipts and photos\",\n            icon: \"Brain\",\n            color: \"blue\"\n        },\n        {\n            id: \"smart-reminders\",\n            name: \"Smart Reminders\",\n            description: \"Never miss warranty expiration or service dates\",\n            icon: \"Bell\",\n            color: \"cyan\"\n        },\n        {\n            id: \"3d-inventory\",\n            name: \"3D Inventory\",\n            description: \"Visualize your items in AR/3D room layouts\",\n            icon: \"Box\",\n            color: \"purple\"\n        },\n        {\n            id: \"claim-assistant\",\n            name: \"Claim Assistant\",\n            description: \"Step-by-step guidance for warranty claims\",\n            icon: \"FileText\",\n            color: \"pink\"\n        }\n    ],\n    advanced: [\n        {\n            id: \"email-sync\",\n            name: \"Email Integration\",\n            description: \"Auto-import receipts from Gmail and other email providers\",\n            icon: \"Mail\",\n            color: \"green\"\n        },\n        {\n            id: \"service-tracking\",\n            name: \"Service Tracking\",\n            description: \"Track maintenance schedules for home and vehicle items\",\n            icon: \"Wrench\",\n            color: \"orange\"\n        },\n        {\n            id: \"ownership-proof\",\n            name: \"Ownership Verification\",\n            description: \"Blockchain-based ownership proof system\",\n            icon: \"Shield\",\n            color: \"red\"\n        },\n        {\n            id: \"family-sharing\",\n            name: \"Family Sharing\",\n            description: \"Share warranty information with family members\",\n            icon: \"Users\",\n            color: \"purple\"\n        }\n    ]\n};\n// Pricing Plans\nconst PRICING_PLANS = [\n    {\n        id: \"free\",\n        name: \"Free\",\n        price: 0,\n        period: \"month\",\n        description: \"Perfect for getting started\",\n        features: [\n            \"Manual upload (5 items/month)\",\n            \"Basic reminders\",\n            \"Simple dashboard\",\n            \"Email support\"\n        ],\n        limitations: [\n            \"Limited to 5 items per month\",\n            \"No AI extraction\",\n            \"No email integration\"\n        ],\n        cta: \"Get Started\",\n        popular: false\n    },\n    {\n        id: \"pro\",\n        name: \"Pro\",\n        price: 9.99,\n        period: \"month\",\n        description: \"For individuals and families\",\n        features: [\n            \"Unlimited items\",\n            \"AI extraction\",\n            \"Email integration\",\n            \"Advanced reminders\",\n            \"AR/3D features\",\n            \"Priority support\"\n        ],\n        limitations: [],\n        cta: \"Start Free Trial\",\n        popular: true\n    },\n    {\n        id: \"family\",\n        name: \"Family\",\n        price: 19.99,\n        period: \"month\",\n        description: \"For families and households\",\n        features: [\n            \"Everything in Pro\",\n            \"Up to 5 users\",\n            \"Shared household items\",\n            \"Family notifications\",\n            \"Advanced analytics\",\n            \"Phone support\"\n        ],\n        limitations: [],\n        cta: \"Start Free Trial\",\n        popular: false\n    },\n    {\n        id: \"business\",\n        name: \"Business\",\n        price: 99,\n        period: \"month\",\n        description: \"For businesses and teams\",\n        features: [\n            \"Everything in Family\",\n            \"Unlimited users\",\n            \"API access\",\n            \"White-label options\",\n            \"Custom integrations\",\n            \"Dedicated support\"\n        ],\n        limitations: [],\n        cta: \"Contact Sales\",\n        popular: false\n    }\n];\n// Testimonials\nconst TESTIMONIALS = [\n    {\n        id: 1,\n        name: \"Sarah Chen\",\n        role: \"Homeowner\",\n        company: \"San Francisco, CA\",\n        content: \"WarrantyAI saved me $800 on my refrigerator repair. I completely forgot about the extended warranty until the app reminded me!\",\n        avatar: \"/testimonials/sarah.jpg\",\n        rating: 5\n    },\n    {\n        id: 2,\n        name: \"Mike Rodriguez\",\n        role: \"Tech Enthusiast\",\n        company: \"Austin, TX\",\n        content: \"The AI extraction is incredible. I just take a photo of my receipt and everything is automatically organized. Game changer!\",\n        avatar: \"/testimonials/mike.jpg\",\n        rating: 5\n    },\n    {\n        id: 3,\n        name: \"Emily Johnson\",\n        role: \"Small Business Owner\",\n        company: \"Denver, CO\",\n        content: \"Managing warranties for all our office equipment was a nightmare. WarrantyAI made it effortless and we never miss service dates.\",\n        avatar: \"/testimonials/emily.jpg\",\n        rating: 5\n    }\n];\n// Statistics\nconst STATS = [\n    {\n        id: \"users\",\n        value: \"50,000+\",\n        label: \"Active Users\",\n        description: \"Trust WarrantyAI with their warranties\"\n    },\n    {\n        id: \"items\",\n        value: \"1.2M+\",\n        label: \"Items Tracked\",\n        description: \"Warranties and services managed\"\n    },\n    {\n        id: \"savings\",\n        value: \"$2.5M+\",\n        label: \"Money Saved\",\n        description: \"In warranty claims and repairs\"\n    },\n    {\n        id: \"accuracy\",\n        value: \"99.2%\",\n        label: \"AI Accuracy\",\n        description: \"Receipt extraction precision\"\n    }\n];\n// Demo Data Categories\nconst DEMO_CATEGORIES = [\n    {\n        id: \"electronics\",\n        name: \"Electronics\",\n        icon: \"Smartphone\",\n        count: 12,\n        color: \"blue\"\n    },\n    {\n        id: \"appliances\",\n        name: \"Home Appliances\",\n        icon: \"Home\",\n        count: 8,\n        color: \"green\"\n    },\n    {\n        id: \"automotive\",\n        name: \"Automotive\",\n        icon: \"Car\",\n        count: 3,\n        color: \"orange\"\n    },\n    {\n        id: \"furniture\",\n        name: \"Furniture\",\n        icon: \"Sofa\",\n        count: 6,\n        color: \"purple\"\n    }\n];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    APP_CONFIG,\n    COLORS,\n    GRADIENTS,\n    FONTS,\n    ANIMATIONS,\n    FX_POOL,\n    NAVIGATION,\n    FEATURES,\n    PRICING_PLANS,\n    TESTIMONIALS,\n    STATS,\n    DEMO_CATEGORIES\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/constants.js\n"));

/***/ })

});