"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/three-stdlib";
exports.ids = ["vendor-chunks/three-stdlib"];
exports.modules = {

/***/ "(ssr)/./node_modules/three-stdlib/_polyfill/constants.js":
/*!**********************************************************!*\
  !*** ./node_modules/three-stdlib/_polyfill/constants.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n\nconst version = /* @__PURE__ */ (() => parseInt(three__WEBPACK_IMPORTED_MODULE_0__.REVISION.replace(/\\D+/g, \"\")))();\n\n//# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdGhyZWUtc3RkbGliL19wb2x5ZmlsbC9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUM7QUFDakMsZ0RBQWdELDJDQUFRO0FBR3REO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ29sZF9cXHBqc1xcaTUtZDMtd2FycmFudHlhaVxcbm9kZV9tb2R1bGVzXFx0aHJlZS1zdGRsaWJcXF9wb2x5ZmlsbFxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFJFVklTSU9OIH0gZnJvbSBcInRocmVlXCI7XG5jb25zdCB2ZXJzaW9uID0gLyogQF9fUFVSRV9fICovICgoKSA9PiBwYXJzZUludChSRVZJU0lPTi5yZXBsYWNlKC9cXEQrL2csIFwiXCIpKSkoKTtcbmV4cG9ydCB7XG4gIHZlcnNpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/_polyfill/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/controls/EventDispatcher.js":
/*!***************************************************************!*\
  !*** ./node_modules/three-stdlib/controls/EventDispatcher.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventDispatcher: () => (/* binding */ EventDispatcher)\n/* harmony export */ });\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\nclass EventDispatcher {\n  constructor() {\n    // not defined in @types/three\n    __publicField(this, \"_listeners\");\n  }\n  /**\n   * Adds a listener to an event type.\n   * @param type The type of event to listen to.\n   * @param listener The function that gets called when the event is fired.\n   */\n  addEventListener(type, listener) {\n    if (this._listeners === void 0)\n      this._listeners = {};\n    const listeners = this._listeners;\n    if (listeners[type] === void 0) {\n      listeners[type] = [];\n    }\n    if (listeners[type].indexOf(listener) === -1) {\n      listeners[type].push(listener);\n    }\n  }\n  /**\n      * Checks if listener is added to an event type.\n      * @param type The type of event to listen to.\n      * @param listener The function that gets called when the event is fired.\n      */\n  hasEventListener(type, listener) {\n    if (this._listeners === void 0)\n      return false;\n    const listeners = this._listeners;\n    return listeners[type] !== void 0 && listeners[type].indexOf(listener) !== -1;\n  }\n  /**\n      * Removes a listener from an event type.\n      * @param type The type of the listener that gets removed.\n      * @param listener The listener function that gets removed.\n      */\n  removeEventListener(type, listener) {\n    if (this._listeners === void 0)\n      return;\n    const listeners = this._listeners;\n    const listenerArray = listeners[type];\n    if (listenerArray !== void 0) {\n      const index = listenerArray.indexOf(listener);\n      if (index !== -1) {\n        listenerArray.splice(index, 1);\n      }\n    }\n  }\n  /**\n      * Fire an event type.\n      * @param event The event that gets fired.\n      */\n  dispatchEvent(event) {\n    if (this._listeners === void 0)\n      return;\n    const listeners = this._listeners;\n    const listenerArray = listeners[event.type];\n    if (listenerArray !== void 0) {\n      event.target = this;\n      const array = listenerArray.slice(0);\n      for (let i = 0, l = array.length; i < l; i++) {\n        array[i].call(this, event);\n      }\n      event.target = null;\n    }\n  }\n}\n\n//# sourceMappingURL=EventDispatcher.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/controls/EventDispatcher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/controls/OrbitControls.js":
/*!*************************************************************!*\
  !*** ./node_modules/three-stdlib/controls/OrbitControls.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapControls: () => (/* binding */ MapControls),\n/* harmony export */   OrbitControls: () => (/* binding */ OrbitControls)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var _EventDispatcher_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EventDispatcher.js */ \"(ssr)/./node_modules/three-stdlib/controls/EventDispatcher.js\");\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => {\n  __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n  return value;\n};\n\n\nconst _ray = /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Ray();\nconst _plane = /* @__PURE__ */ new three__WEBPACK_IMPORTED_MODULE_0__.Plane();\nconst TILT_LIMIT = Math.cos(70 * (Math.PI / 180));\nconst moduloWrapAround = (offset, capacity) => (offset % capacity + capacity) % capacity;\nclass OrbitControls extends _EventDispatcher_js__WEBPACK_IMPORTED_MODULE_1__.EventDispatcher {\n  constructor(object, domElement) {\n    super();\n    __publicField(this, \"object\");\n    __publicField(this, \"domElement\");\n    // Set to false to disable this control\n    __publicField(this, \"enabled\", true);\n    // \"target\" sets the location of focus, where the object orbits around\n    __publicField(this, \"target\", new three__WEBPACK_IMPORTED_MODULE_0__.Vector3());\n    // How far you can dolly in and out ( PerspectiveCamera only )\n    __publicField(this, \"minDistance\", 0);\n    __publicField(this, \"maxDistance\", Infinity);\n    // How far you can zoom in and out ( OrthographicCamera only )\n    __publicField(this, \"minZoom\", 0);\n    __publicField(this, \"maxZoom\", Infinity);\n    // How far you can orbit vertically, upper and lower limits.\n    // Range is 0 to Math.PI radians.\n    __publicField(this, \"minPolarAngle\", 0);\n    // radians\n    __publicField(this, \"maxPolarAngle\", Math.PI);\n    // radians\n    // How far you can orbit horizontally, upper and lower limits.\n    // If set, the interval [ min, max ] must be a sub-interval of [ - 2 PI, 2 PI ], with ( max - min < 2 PI )\n    __publicField(this, \"minAzimuthAngle\", -Infinity);\n    // radians\n    __publicField(this, \"maxAzimuthAngle\", Infinity);\n    // radians\n    // Set to true to enable damping (inertia)\n    // If damping is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"enableDamping\", false);\n    __publicField(this, \"dampingFactor\", 0.05);\n    // This option actually enables dollying in and out; left as \"zoom\" for backwards compatibility.\n    // Set to false to disable zooming\n    __publicField(this, \"enableZoom\", true);\n    __publicField(this, \"zoomSpeed\", 1);\n    // Set to false to disable rotating\n    __publicField(this, \"enableRotate\", true);\n    __publicField(this, \"rotateSpeed\", 1);\n    // Set to false to disable panning\n    __publicField(this, \"enablePan\", true);\n    __publicField(this, \"panSpeed\", 1);\n    __publicField(this, \"screenSpacePanning\", true);\n    // if false, pan orthogonal to world-space direction camera.up\n    __publicField(this, \"keyPanSpeed\", 7);\n    // pixels moved per arrow key push\n    __publicField(this, \"zoomToCursor\", false);\n    // Set to true to automatically rotate around the target\n    // If auto-rotate is enabled, you must call controls.update() in your animation loop\n    __publicField(this, \"autoRotate\", false);\n    __publicField(this, \"autoRotateSpeed\", 2);\n    // 30 seconds per orbit when fps is 60\n    __publicField(this, \"reverseOrbit\", false);\n    // true if you want to reverse the orbit to mouse drag from left to right = orbits left\n    __publicField(this, \"reverseHorizontalOrbit\", false);\n    // true if you want to reverse the horizontal orbit direction\n    __publicField(this, \"reverseVerticalOrbit\", false);\n    // true if you want to reverse the vertical orbit direction\n    // The four arrow keys\n    __publicField(this, \"keys\", { LEFT: \"ArrowLeft\", UP: \"ArrowUp\", RIGHT: \"ArrowRight\", BOTTOM: \"ArrowDown\" });\n    // Mouse buttons\n    __publicField(this, \"mouseButtons\", {\n      LEFT: three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.ROTATE,\n      MIDDLE: three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.DOLLY,\n      RIGHT: three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.PAN\n    });\n    // Touch fingers\n    __publicField(this, \"touches\", { ONE: three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.ROTATE, TWO: three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_PAN });\n    __publicField(this, \"target0\");\n    __publicField(this, \"position0\");\n    __publicField(this, \"zoom0\");\n    // the target DOM element for key events\n    __publicField(this, \"_domElementKeyEvents\", null);\n    __publicField(this, \"getPolarAngle\");\n    __publicField(this, \"getAzimuthalAngle\");\n    __publicField(this, \"setPolarAngle\");\n    __publicField(this, \"setAzimuthalAngle\");\n    __publicField(this, \"getDistance\");\n    // Not used in most scenarios, however they can be useful for specific use cases\n    __publicField(this, \"getZoomScale\");\n    __publicField(this, \"listenToKeyEvents\");\n    __publicField(this, \"stopListenToKeyEvents\");\n    __publicField(this, \"saveState\");\n    __publicField(this, \"reset\");\n    __publicField(this, \"update\");\n    __publicField(this, \"connect\");\n    __publicField(this, \"dispose\");\n    // Dolly in programmatically\n    __publicField(this, \"dollyIn\");\n    // Dolly out programmatically\n    __publicField(this, \"dollyOut\");\n    // Get the current scale\n    __publicField(this, \"getScale\");\n    // Set the current scale (these are not used in most scenarios, however they can be useful for specific use cases)\n    __publicField(this, \"setScale\");\n    this.object = object;\n    this.domElement = domElement;\n    this.target0 = this.target.clone();\n    this.position0 = this.object.position.clone();\n    this.zoom0 = this.object.zoom;\n    this.getPolarAngle = () => spherical.phi;\n    this.getAzimuthalAngle = () => spherical.theta;\n    this.setPolarAngle = (value) => {\n      let phi = moduloWrapAround(value, 2 * Math.PI);\n      let currentPhi = spherical.phi;\n      if (currentPhi < 0)\n        currentPhi += 2 * Math.PI;\n      if (phi < 0)\n        phi += 2 * Math.PI;\n      let phiDist = Math.abs(phi - currentPhi);\n      if (2 * Math.PI - phiDist < phiDist) {\n        if (phi < currentPhi) {\n          phi += 2 * Math.PI;\n        } else {\n          currentPhi += 2 * Math.PI;\n        }\n      }\n      sphericalDelta.phi = phi - currentPhi;\n      scope.update();\n    };\n    this.setAzimuthalAngle = (value) => {\n      let theta = moduloWrapAround(value, 2 * Math.PI);\n      let currentTheta = spherical.theta;\n      if (currentTheta < 0)\n        currentTheta += 2 * Math.PI;\n      if (theta < 0)\n        theta += 2 * Math.PI;\n      let thetaDist = Math.abs(theta - currentTheta);\n      if (2 * Math.PI - thetaDist < thetaDist) {\n        if (theta < currentTheta) {\n          theta += 2 * Math.PI;\n        } else {\n          currentTheta += 2 * Math.PI;\n        }\n      }\n      sphericalDelta.theta = theta - currentTheta;\n      scope.update();\n    };\n    this.getDistance = () => scope.object.position.distanceTo(scope.target);\n    this.listenToKeyEvents = (domElement2) => {\n      domElement2.addEventListener(\"keydown\", onKeyDown);\n      this._domElementKeyEvents = domElement2;\n    };\n    this.stopListenToKeyEvents = () => {\n      this._domElementKeyEvents.removeEventListener(\"keydown\", onKeyDown);\n      this._domElementKeyEvents = null;\n    };\n    this.saveState = () => {\n      scope.target0.copy(scope.target);\n      scope.position0.copy(scope.object.position);\n      scope.zoom0 = scope.object.zoom;\n    };\n    this.reset = () => {\n      scope.target.copy(scope.target0);\n      scope.object.position.copy(scope.position0);\n      scope.object.zoom = scope.zoom0;\n      scope.object.updateProjectionMatrix();\n      scope.dispatchEvent(changeEvent);\n      scope.update();\n      state = STATE.NONE;\n    };\n    this.update = (() => {\n      const offset = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      const up = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(0, 1, 0);\n      const quat = new three__WEBPACK_IMPORTED_MODULE_0__.Quaternion().setFromUnitVectors(object.up, up);\n      const quatInverse = quat.clone().invert();\n      const lastPosition = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      const lastQuaternion = new three__WEBPACK_IMPORTED_MODULE_0__.Quaternion();\n      const twoPI = 2 * Math.PI;\n      return function update() {\n        const position = scope.object.position;\n        quat.setFromUnitVectors(object.up, up);\n        quatInverse.copy(quat).invert();\n        offset.copy(position).sub(scope.target);\n        offset.applyQuaternion(quat);\n        spherical.setFromVector3(offset);\n        if (scope.autoRotate && state === STATE.NONE) {\n          rotateLeft(getAutoRotationAngle());\n        }\n        if (scope.enableDamping) {\n          spherical.theta += sphericalDelta.theta * scope.dampingFactor;\n          spherical.phi += sphericalDelta.phi * scope.dampingFactor;\n        } else {\n          spherical.theta += sphericalDelta.theta;\n          spherical.phi += sphericalDelta.phi;\n        }\n        let min = scope.minAzimuthAngle;\n        let max = scope.maxAzimuthAngle;\n        if (isFinite(min) && isFinite(max)) {\n          if (min < -Math.PI)\n            min += twoPI;\n          else if (min > Math.PI)\n            min -= twoPI;\n          if (max < -Math.PI)\n            max += twoPI;\n          else if (max > Math.PI)\n            max -= twoPI;\n          if (min <= max) {\n            spherical.theta = Math.max(min, Math.min(max, spherical.theta));\n          } else {\n            spherical.theta = spherical.theta > (min + max) / 2 ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta);\n          }\n        }\n        spherical.phi = Math.max(scope.minPolarAngle, Math.min(scope.maxPolarAngle, spherical.phi));\n        spherical.makeSafe();\n        if (scope.enableDamping === true) {\n          scope.target.addScaledVector(panOffset, scope.dampingFactor);\n        } else {\n          scope.target.add(panOffset);\n        }\n        if (scope.zoomToCursor && performCursorZoom || scope.object.isOrthographicCamera) {\n          spherical.radius = clampDistance(spherical.radius);\n        } else {\n          spherical.radius = clampDistance(spherical.radius * scale);\n        }\n        offset.setFromSpherical(spherical);\n        offset.applyQuaternion(quatInverse);\n        position.copy(scope.target).add(offset);\n        if (!scope.object.matrixAutoUpdate)\n          scope.object.updateMatrix();\n        scope.object.lookAt(scope.target);\n        if (scope.enableDamping === true) {\n          sphericalDelta.theta *= 1 - scope.dampingFactor;\n          sphericalDelta.phi *= 1 - scope.dampingFactor;\n          panOffset.multiplyScalar(1 - scope.dampingFactor);\n        } else {\n          sphericalDelta.set(0, 0, 0);\n          panOffset.set(0, 0, 0);\n        }\n        let zoomChanged = false;\n        if (scope.zoomToCursor && performCursorZoom) {\n          let newRadius = null;\n          if (scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.PerspectiveCamera && scope.object.isPerspectiveCamera) {\n            const prevRadius = offset.length();\n            newRadius = clampDistance(prevRadius * scale);\n            const radiusDelta = prevRadius - newRadius;\n            scope.object.position.addScaledVector(dollyDirection, radiusDelta);\n            scope.object.updateMatrixWorld();\n          } else if (scope.object.isOrthographicCamera) {\n            const mouseBefore = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(mouse.x, mouse.y, 0);\n            mouseBefore.unproject(scope.object);\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale));\n            scope.object.updateProjectionMatrix();\n            zoomChanged = true;\n            const mouseAfter = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3(mouse.x, mouse.y, 0);\n            mouseAfter.unproject(scope.object);\n            scope.object.position.sub(mouseAfter).add(mouseBefore);\n            scope.object.updateMatrixWorld();\n            newRadius = offset.length();\n          } else {\n            console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled.\");\n            scope.zoomToCursor = false;\n          }\n          if (newRadius !== null) {\n            if (scope.screenSpacePanning) {\n              scope.target.set(0, 0, -1).transformDirection(scope.object.matrix).multiplyScalar(newRadius).add(scope.object.position);\n            } else {\n              _ray.origin.copy(scope.object.position);\n              _ray.direction.set(0, 0, -1).transformDirection(scope.object.matrix);\n              if (Math.abs(scope.object.up.dot(_ray.direction)) < TILT_LIMIT) {\n                object.lookAt(scope.target);\n              } else {\n                _plane.setFromNormalAndCoplanarPoint(scope.object.up, scope.target);\n                _ray.intersectPlane(_plane, scope.target);\n              }\n            }\n          }\n        } else if (scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera && scope.object.isOrthographicCamera) {\n          zoomChanged = scale !== 1;\n          if (zoomChanged) {\n            scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / scale));\n            scope.object.updateProjectionMatrix();\n          }\n        }\n        scale = 1;\n        performCursorZoom = false;\n        if (zoomChanged || lastPosition.distanceToSquared(scope.object.position) > EPS || 8 * (1 - lastQuaternion.dot(scope.object.quaternion)) > EPS) {\n          scope.dispatchEvent(changeEvent);\n          lastPosition.copy(scope.object.position);\n          lastQuaternion.copy(scope.object.quaternion);\n          zoomChanged = false;\n          return true;\n        }\n        return false;\n      };\n    })();\n    this.connect = (domElement2) => {\n      scope.domElement = domElement2;\n      scope.domElement.style.touchAction = \"none\";\n      scope.domElement.addEventListener(\"contextmenu\", onContextMenu);\n      scope.domElement.addEventListener(\"pointerdown\", onPointerDown);\n      scope.domElement.addEventListener(\"pointercancel\", onPointerUp);\n      scope.domElement.addEventListener(\"wheel\", onMouseWheel);\n    };\n    this.dispose = () => {\n      var _a, _b, _c, _d, _e, _f;\n      if (scope.domElement) {\n        scope.domElement.style.touchAction = \"auto\";\n      }\n      (_a = scope.domElement) == null ? void 0 : _a.removeEventListener(\"contextmenu\", onContextMenu);\n      (_b = scope.domElement) == null ? void 0 : _b.removeEventListener(\"pointerdown\", onPointerDown);\n      (_c = scope.domElement) == null ? void 0 : _c.removeEventListener(\"pointercancel\", onPointerUp);\n      (_d = scope.domElement) == null ? void 0 : _d.removeEventListener(\"wheel\", onMouseWheel);\n      (_e = scope.domElement) == null ? void 0 : _e.ownerDocument.removeEventListener(\"pointermove\", onPointerMove);\n      (_f = scope.domElement) == null ? void 0 : _f.ownerDocument.removeEventListener(\"pointerup\", onPointerUp);\n      if (scope._domElementKeyEvents !== null) {\n        scope._domElementKeyEvents.removeEventListener(\"keydown\", onKeyDown);\n      }\n    };\n    const scope = this;\n    const changeEvent = { type: \"change\" };\n    const startEvent = { type: \"start\" };\n    const endEvent = { type: \"end\" };\n    const STATE = {\n      NONE: -1,\n      ROTATE: 0,\n      DOLLY: 1,\n      PAN: 2,\n      TOUCH_ROTATE: 3,\n      TOUCH_PAN: 4,\n      TOUCH_DOLLY_PAN: 5,\n      TOUCH_DOLLY_ROTATE: 6\n    };\n    let state = STATE.NONE;\n    const EPS = 1e-6;\n    const spherical = new three__WEBPACK_IMPORTED_MODULE_0__.Spherical();\n    const sphericalDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Spherical();\n    let scale = 1;\n    const panOffset = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n    const rotateStart = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const rotateEnd = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const rotateDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const panStart = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const panEnd = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const panDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyStart = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyEnd = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyDelta = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    const dollyDirection = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n    const mouse = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n    let performCursorZoom = false;\n    const pointers = [];\n    const pointerPositions = {};\n    function getAutoRotationAngle() {\n      return 2 * Math.PI / 60 / 60 * scope.autoRotateSpeed;\n    }\n    function getZoomScale() {\n      return Math.pow(0.95, scope.zoomSpeed);\n    }\n    function rotateLeft(angle) {\n      if (scope.reverseOrbit || scope.reverseHorizontalOrbit) {\n        sphericalDelta.theta += angle;\n      } else {\n        sphericalDelta.theta -= angle;\n      }\n    }\n    function rotateUp(angle) {\n      if (scope.reverseOrbit || scope.reverseVerticalOrbit) {\n        sphericalDelta.phi += angle;\n      } else {\n        sphericalDelta.phi -= angle;\n      }\n    }\n    const panLeft = (() => {\n      const v = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      return function panLeft2(distance, objectMatrix) {\n        v.setFromMatrixColumn(objectMatrix, 0);\n        v.multiplyScalar(-distance);\n        panOffset.add(v);\n      };\n    })();\n    const panUp = (() => {\n      const v = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      return function panUp2(distance, objectMatrix) {\n        if (scope.screenSpacePanning === true) {\n          v.setFromMatrixColumn(objectMatrix, 1);\n        } else {\n          v.setFromMatrixColumn(objectMatrix, 0);\n          v.crossVectors(scope.object.up, v);\n        }\n        v.multiplyScalar(distance);\n        panOffset.add(v);\n      };\n    })();\n    const pan = (() => {\n      const offset = new three__WEBPACK_IMPORTED_MODULE_0__.Vector3();\n      return function pan2(deltaX, deltaY) {\n        const element = scope.domElement;\n        if (element && scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.PerspectiveCamera && scope.object.isPerspectiveCamera) {\n          const position = scope.object.position;\n          offset.copy(position).sub(scope.target);\n          let targetDistance = offset.length();\n          targetDistance *= Math.tan(scope.object.fov / 2 * Math.PI / 180);\n          panLeft(2 * deltaX * targetDistance / element.clientHeight, scope.object.matrix);\n          panUp(2 * deltaY * targetDistance / element.clientHeight, scope.object.matrix);\n        } else if (element && scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera && scope.object.isOrthographicCamera) {\n          panLeft(\n            deltaX * (scope.object.right - scope.object.left) / scope.object.zoom / element.clientWidth,\n            scope.object.matrix\n          );\n          panUp(\n            deltaY * (scope.object.top - scope.object.bottom) / scope.object.zoom / element.clientHeight,\n            scope.object.matrix\n          );\n        } else {\n          console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.\");\n          scope.enablePan = false;\n        }\n      };\n    })();\n    function setScale(newScale) {\n      if (scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.PerspectiveCamera && scope.object.isPerspectiveCamera || scope.object instanceof three__WEBPACK_IMPORTED_MODULE_0__.OrthographicCamera && scope.object.isOrthographicCamera) {\n        scale = newScale;\n      } else {\n        console.warn(\"WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.\");\n        scope.enableZoom = false;\n      }\n    }\n    function dollyOut(dollyScale) {\n      setScale(scale / dollyScale);\n    }\n    function dollyIn(dollyScale) {\n      setScale(scale * dollyScale);\n    }\n    function updateMouseParameters(event) {\n      if (!scope.zoomToCursor || !scope.domElement) {\n        return;\n      }\n      performCursorZoom = true;\n      const rect = scope.domElement.getBoundingClientRect();\n      const x = event.clientX - rect.left;\n      const y = event.clientY - rect.top;\n      const w = rect.width;\n      const h = rect.height;\n      mouse.x = x / w * 2 - 1;\n      mouse.y = -(y / h) * 2 + 1;\n      dollyDirection.set(mouse.x, mouse.y, 1).unproject(scope.object).sub(scope.object.position).normalize();\n    }\n    function clampDistance(dist) {\n      return Math.max(scope.minDistance, Math.min(scope.maxDistance, dist));\n    }\n    function handleMouseDownRotate(event) {\n      rotateStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseDownDolly(event) {\n      updateMouseParameters(event);\n      dollyStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseDownPan(event) {\n      panStart.set(event.clientX, event.clientY);\n    }\n    function handleMouseMoveRotate(event) {\n      rotateEnd.set(event.clientX, event.clientY);\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);\n      const element = scope.domElement;\n      if (element) {\n        rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);\n        rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);\n      }\n      rotateStart.copy(rotateEnd);\n      scope.update();\n    }\n    function handleMouseMoveDolly(event) {\n      dollyEnd.set(event.clientX, event.clientY);\n      dollyDelta.subVectors(dollyEnd, dollyStart);\n      if (dollyDelta.y > 0) {\n        dollyOut(getZoomScale());\n      } else if (dollyDelta.y < 0) {\n        dollyIn(getZoomScale());\n      }\n      dollyStart.copy(dollyEnd);\n      scope.update();\n    }\n    function handleMouseMovePan(event) {\n      panEnd.set(event.clientX, event.clientY);\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);\n      pan(panDelta.x, panDelta.y);\n      panStart.copy(panEnd);\n      scope.update();\n    }\n    function handleMouseWheel(event) {\n      updateMouseParameters(event);\n      if (event.deltaY < 0) {\n        dollyIn(getZoomScale());\n      } else if (event.deltaY > 0) {\n        dollyOut(getZoomScale());\n      }\n      scope.update();\n    }\n    function handleKeyDown(event) {\n      let needsUpdate = false;\n      switch (event.code) {\n        case scope.keys.UP:\n          pan(0, scope.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case scope.keys.BOTTOM:\n          pan(0, -scope.keyPanSpeed);\n          needsUpdate = true;\n          break;\n        case scope.keys.LEFT:\n          pan(scope.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n        case scope.keys.RIGHT:\n          pan(-scope.keyPanSpeed, 0);\n          needsUpdate = true;\n          break;\n      }\n      if (needsUpdate) {\n        event.preventDefault();\n        scope.update();\n      }\n    }\n    function handleTouchStartRotate() {\n      if (pointers.length == 1) {\n        rotateStart.set(pointers[0].pageX, pointers[0].pageY);\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX);\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY);\n        rotateStart.set(x, y);\n      }\n    }\n    function handleTouchStartPan() {\n      if (pointers.length == 1) {\n        panStart.set(pointers[0].pageX, pointers[0].pageY);\n      } else {\n        const x = 0.5 * (pointers[0].pageX + pointers[1].pageX);\n        const y = 0.5 * (pointers[0].pageY + pointers[1].pageY);\n        panStart.set(x, y);\n      }\n    }\n    function handleTouchStartDolly() {\n      const dx = pointers[0].pageX - pointers[1].pageX;\n      const dy = pointers[0].pageY - pointers[1].pageY;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      dollyStart.set(0, distance);\n    }\n    function handleTouchStartDollyPan() {\n      if (scope.enableZoom)\n        handleTouchStartDolly();\n      if (scope.enablePan)\n        handleTouchStartPan();\n    }\n    function handleTouchStartDollyRotate() {\n      if (scope.enableZoom)\n        handleTouchStartDolly();\n      if (scope.enableRotate)\n        handleTouchStartRotate();\n    }\n    function handleTouchMoveRotate(event) {\n      if (pointers.length == 1) {\n        rotateEnd.set(event.pageX, event.pageY);\n      } else {\n        const position = getSecondPointerPosition(event);\n        const x = 0.5 * (event.pageX + position.x);\n        const y = 0.5 * (event.pageY + position.y);\n        rotateEnd.set(x, y);\n      }\n      rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);\n      const element = scope.domElement;\n      if (element) {\n        rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);\n        rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);\n      }\n      rotateStart.copy(rotateEnd);\n    }\n    function handleTouchMovePan(event) {\n      if (pointers.length == 1) {\n        panEnd.set(event.pageX, event.pageY);\n      } else {\n        const position = getSecondPointerPosition(event);\n        const x = 0.5 * (event.pageX + position.x);\n        const y = 0.5 * (event.pageY + position.y);\n        panEnd.set(x, y);\n      }\n      panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);\n      pan(panDelta.x, panDelta.y);\n      panStart.copy(panEnd);\n    }\n    function handleTouchMoveDolly(event) {\n      const position = getSecondPointerPosition(event);\n      const dx = event.pageX - position.x;\n      const dy = event.pageY - position.y;\n      const distance = Math.sqrt(dx * dx + dy * dy);\n      dollyEnd.set(0, distance);\n      dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));\n      dollyOut(dollyDelta.y);\n      dollyStart.copy(dollyEnd);\n    }\n    function handleTouchMoveDollyPan(event) {\n      if (scope.enableZoom)\n        handleTouchMoveDolly(event);\n      if (scope.enablePan)\n        handleTouchMovePan(event);\n    }\n    function handleTouchMoveDollyRotate(event) {\n      if (scope.enableZoom)\n        handleTouchMoveDolly(event);\n      if (scope.enableRotate)\n        handleTouchMoveRotate(event);\n    }\n    function onPointerDown(event) {\n      var _a, _b;\n      if (scope.enabled === false)\n        return;\n      if (pointers.length === 0) {\n        (_a = scope.domElement) == null ? void 0 : _a.ownerDocument.addEventListener(\"pointermove\", onPointerMove);\n        (_b = scope.domElement) == null ? void 0 : _b.ownerDocument.addEventListener(\"pointerup\", onPointerUp);\n      }\n      addPointer(event);\n      if (event.pointerType === \"touch\") {\n        onTouchStart(event);\n      } else {\n        onMouseDown(event);\n      }\n    }\n    function onPointerMove(event) {\n      if (scope.enabled === false)\n        return;\n      if (event.pointerType === \"touch\") {\n        onTouchMove(event);\n      } else {\n        onMouseMove(event);\n      }\n    }\n    function onPointerUp(event) {\n      var _a, _b, _c;\n      removePointer(event);\n      if (pointers.length === 0) {\n        (_a = scope.domElement) == null ? void 0 : _a.releasePointerCapture(event.pointerId);\n        (_b = scope.domElement) == null ? void 0 : _b.ownerDocument.removeEventListener(\"pointermove\", onPointerMove);\n        (_c = scope.domElement) == null ? void 0 : _c.ownerDocument.removeEventListener(\"pointerup\", onPointerUp);\n      }\n      scope.dispatchEvent(endEvent);\n      state = STATE.NONE;\n    }\n    function onMouseDown(event) {\n      let mouseAction;\n      switch (event.button) {\n        case 0:\n          mouseAction = scope.mouseButtons.LEFT;\n          break;\n        case 1:\n          mouseAction = scope.mouseButtons.MIDDLE;\n          break;\n        case 2:\n          mouseAction = scope.mouseButtons.RIGHT;\n          break;\n        default:\n          mouseAction = -1;\n      }\n      switch (mouseAction) {\n        case three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.DOLLY:\n          if (scope.enableZoom === false)\n            return;\n          handleMouseDownDolly(event);\n          state = STATE.DOLLY;\n          break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.ROTATE:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enablePan === false)\n              return;\n            handleMouseDownPan(event);\n            state = STATE.PAN;\n          } else {\n            if (scope.enableRotate === false)\n              return;\n            handleMouseDownRotate(event);\n            state = STATE.ROTATE;\n          }\n          break;\n        case three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.PAN:\n          if (event.ctrlKey || event.metaKey || event.shiftKey) {\n            if (scope.enableRotate === false)\n              return;\n            handleMouseDownRotate(event);\n            state = STATE.ROTATE;\n          } else {\n            if (scope.enablePan === false)\n              return;\n            handleMouseDownPan(event);\n            state = STATE.PAN;\n          }\n          break;\n        default:\n          state = STATE.NONE;\n      }\n      if (state !== STATE.NONE) {\n        scope.dispatchEvent(startEvent);\n      }\n    }\n    function onMouseMove(event) {\n      if (scope.enabled === false)\n        return;\n      switch (state) {\n        case STATE.ROTATE:\n          if (scope.enableRotate === false)\n            return;\n          handleMouseMoveRotate(event);\n          break;\n        case STATE.DOLLY:\n          if (scope.enableZoom === false)\n            return;\n          handleMouseMoveDolly(event);\n          break;\n        case STATE.PAN:\n          if (scope.enablePan === false)\n            return;\n          handleMouseMovePan(event);\n          break;\n      }\n    }\n    function onMouseWheel(event) {\n      if (scope.enabled === false || scope.enableZoom === false || state !== STATE.NONE && state !== STATE.ROTATE) {\n        return;\n      }\n      event.preventDefault();\n      scope.dispatchEvent(startEvent);\n      handleMouseWheel(event);\n      scope.dispatchEvent(endEvent);\n    }\n    function onKeyDown(event) {\n      if (scope.enabled === false || scope.enablePan === false)\n        return;\n      handleKeyDown(event);\n    }\n    function onTouchStart(event) {\n      trackPointer(event);\n      switch (pointers.length) {\n        case 1:\n          switch (scope.touches.ONE) {\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.ROTATE:\n              if (scope.enableRotate === false)\n                return;\n              handleTouchStartRotate();\n              state = STATE.TOUCH_ROTATE;\n              break;\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.PAN:\n              if (scope.enablePan === false)\n                return;\n              handleTouchStartPan();\n              state = STATE.TOUCH_PAN;\n              break;\n            default:\n              state = STATE.NONE;\n          }\n          break;\n        case 2:\n          switch (scope.touches.TWO) {\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_PAN:\n              if (scope.enableZoom === false && scope.enablePan === false)\n                return;\n              handleTouchStartDollyPan();\n              state = STATE.TOUCH_DOLLY_PAN;\n              break;\n            case three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_ROTATE:\n              if (scope.enableZoom === false && scope.enableRotate === false)\n                return;\n              handleTouchStartDollyRotate();\n              state = STATE.TOUCH_DOLLY_ROTATE;\n              break;\n            default:\n              state = STATE.NONE;\n          }\n          break;\n        default:\n          state = STATE.NONE;\n      }\n      if (state !== STATE.NONE) {\n        scope.dispatchEvent(startEvent);\n      }\n    }\n    function onTouchMove(event) {\n      trackPointer(event);\n      switch (state) {\n        case STATE.TOUCH_ROTATE:\n          if (scope.enableRotate === false)\n            return;\n          handleTouchMoveRotate(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_PAN:\n          if (scope.enablePan === false)\n            return;\n          handleTouchMovePan(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_DOLLY_PAN:\n          if (scope.enableZoom === false && scope.enablePan === false)\n            return;\n          handleTouchMoveDollyPan(event);\n          scope.update();\n          break;\n        case STATE.TOUCH_DOLLY_ROTATE:\n          if (scope.enableZoom === false && scope.enableRotate === false)\n            return;\n          handleTouchMoveDollyRotate(event);\n          scope.update();\n          break;\n        default:\n          state = STATE.NONE;\n      }\n    }\n    function onContextMenu(event) {\n      if (scope.enabled === false)\n        return;\n      event.preventDefault();\n    }\n    function addPointer(event) {\n      pointers.push(event);\n    }\n    function removePointer(event) {\n      delete pointerPositions[event.pointerId];\n      for (let i = 0; i < pointers.length; i++) {\n        if (pointers[i].pointerId == event.pointerId) {\n          pointers.splice(i, 1);\n          return;\n        }\n      }\n    }\n    function trackPointer(event) {\n      let position = pointerPositions[event.pointerId];\n      if (position === void 0) {\n        position = new three__WEBPACK_IMPORTED_MODULE_0__.Vector2();\n        pointerPositions[event.pointerId] = position;\n      }\n      position.set(event.pageX, event.pageY);\n    }\n    function getSecondPointerPosition(event) {\n      const pointer = event.pointerId === pointers[0].pointerId ? pointers[1] : pointers[0];\n      return pointerPositions[pointer.pointerId];\n    }\n    this.dollyIn = (dollyScale = getZoomScale()) => {\n      dollyIn(dollyScale);\n      scope.update();\n    };\n    this.dollyOut = (dollyScale = getZoomScale()) => {\n      dollyOut(dollyScale);\n      scope.update();\n    };\n    this.getScale = () => {\n      return scale;\n    };\n    this.setScale = (newScale) => {\n      setScale(newScale);\n      scope.update();\n    };\n    this.getZoomScale = () => {\n      return getZoomScale();\n    };\n    if (domElement !== void 0)\n      this.connect(domElement);\n    this.update();\n  }\n}\nclass MapControls extends OrbitControls {\n  constructor(object, domElement) {\n    super(object, domElement);\n    this.screenSpacePanning = false;\n    this.mouseButtons.LEFT = three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.PAN;\n    this.mouseButtons.RIGHT = three__WEBPACK_IMPORTED_MODULE_0__.MOUSE.ROTATE;\n    this.touches.ONE = three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.PAN;\n    this.touches.TWO = three__WEBPACK_IMPORTED_MODULE_0__.TOUCH.DOLLY_ROTATE;\n  }\n}\n\n//# sourceMappingURL=OrbitControls.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/controls/OrbitControls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/loaders/EXRLoader.js":
/*!********************************************************!*\
  !*** ./node_modules/three-stdlib/loaders/EXRLoader.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EXRLoader: () => (/* binding */ EXRLoader)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var fflate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fflate */ \"(ssr)/./node_modules/three-stdlib/node_modules/fflate/esm/index.mjs\");\n/* harmony import */ var _polyfill_constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_polyfill/constants.js */ \"(ssr)/./node_modules/three-stdlib/_polyfill/constants.js\");\n\n\n\nconst hasColorSpace = _polyfill_constants_js__WEBPACK_IMPORTED_MODULE_0__.version >= 152;\nclass EXRLoader extends three__WEBPACK_IMPORTED_MODULE_1__.DataTextureLoader {\n  constructor(manager) {\n    super(manager);\n    this.type = three__WEBPACK_IMPORTED_MODULE_1__.HalfFloatType;\n  }\n  parse(buffer) {\n    const USHORT_RANGE = 1 << 16;\n    const BITMAP_SIZE = USHORT_RANGE >> 3;\n    const HUF_ENCBITS = 16;\n    const HUF_DECBITS = 14;\n    const HUF_ENCSIZE = (1 << HUF_ENCBITS) + 1;\n    const HUF_DECSIZE = 1 << HUF_DECBITS;\n    const HUF_DECMASK = HUF_DECSIZE - 1;\n    const NBITS = 16;\n    const A_OFFSET = 1 << NBITS - 1;\n    const MOD_MASK = (1 << NBITS) - 1;\n    const SHORT_ZEROCODE_RUN = 59;\n    const LONG_ZEROCODE_RUN = 63;\n    const SHORTEST_LONG_RUN = 2 + LONG_ZEROCODE_RUN - SHORT_ZEROCODE_RUN;\n    const ULONG_SIZE = 8;\n    const FLOAT32_SIZE = 4;\n    const INT32_SIZE = 4;\n    const INT16_SIZE = 2;\n    const INT8_SIZE = 1;\n    const STATIC_HUFFMAN = 0;\n    const DEFLATE = 1;\n    const UNKNOWN = 0;\n    const LOSSY_DCT = 1;\n    const RLE = 2;\n    const logBase = Math.pow(2.7182818, 2.2);\n    function reverseLutFromBitmap(bitmap, lut) {\n      var k = 0;\n      for (var i = 0; i < USHORT_RANGE; ++i) {\n        if (i == 0 || bitmap[i >> 3] & 1 << (i & 7)) {\n          lut[k++] = i;\n        }\n      }\n      var n = k - 1;\n      while (k < USHORT_RANGE)\n        lut[k++] = 0;\n      return n;\n    }\n    function hufClearDecTable(hdec) {\n      for (var i = 0; i < HUF_DECSIZE; i++) {\n        hdec[i] = {};\n        hdec[i].len = 0;\n        hdec[i].lit = 0;\n        hdec[i].p = null;\n      }\n    }\n    const getBitsReturn = { l: 0, c: 0, lc: 0 };\n    function getBits(nBits, c, lc, uInt8Array2, inOffset) {\n      while (lc < nBits) {\n        c = c << 8 | parseUint8Array(uInt8Array2, inOffset);\n        lc += 8;\n      }\n      lc -= nBits;\n      getBitsReturn.l = c >> lc & (1 << nBits) - 1;\n      getBitsReturn.c = c;\n      getBitsReturn.lc = lc;\n    }\n    const hufTableBuffer = new Array(59);\n    function hufCanonicalCodeTable(hcode) {\n      for (var i = 0; i <= 58; ++i)\n        hufTableBuffer[i] = 0;\n      for (var i = 0; i < HUF_ENCSIZE; ++i)\n        hufTableBuffer[hcode[i]] += 1;\n      var c = 0;\n      for (var i = 58; i > 0; --i) {\n        var nc = c + hufTableBuffer[i] >> 1;\n        hufTableBuffer[i] = c;\n        c = nc;\n      }\n      for (var i = 0; i < HUF_ENCSIZE; ++i) {\n        var l = hcode[i];\n        if (l > 0)\n          hcode[i] = l | hufTableBuffer[l]++ << 6;\n      }\n    }\n    function hufUnpackEncTable(uInt8Array2, inDataView, inOffset, ni, im, iM, hcode) {\n      var p = inOffset;\n      var c = 0;\n      var lc = 0;\n      for (; im <= iM; im++) {\n        if (p.value - inOffset.value > ni)\n          return false;\n        getBits(6, c, lc, uInt8Array2, p);\n        var l = getBitsReturn.l;\n        c = getBitsReturn.c;\n        lc = getBitsReturn.lc;\n        hcode[im] = l;\n        if (l == LONG_ZEROCODE_RUN) {\n          if (p.value - inOffset.value > ni) {\n            throw \"Something wrong with hufUnpackEncTable\";\n          }\n          getBits(8, c, lc, uInt8Array2, p);\n          var zerun = getBitsReturn.l + SHORTEST_LONG_RUN;\n          c = getBitsReturn.c;\n          lc = getBitsReturn.lc;\n          if (im + zerun > iM + 1) {\n            throw \"Something wrong with hufUnpackEncTable\";\n          }\n          while (zerun--)\n            hcode[im++] = 0;\n          im--;\n        } else if (l >= SHORT_ZEROCODE_RUN) {\n          var zerun = l - SHORT_ZEROCODE_RUN + 2;\n          if (im + zerun > iM + 1) {\n            throw \"Something wrong with hufUnpackEncTable\";\n          }\n          while (zerun--)\n            hcode[im++] = 0;\n          im--;\n        }\n      }\n      hufCanonicalCodeTable(hcode);\n    }\n    function hufLength(code) {\n      return code & 63;\n    }\n    function hufCode(code) {\n      return code >> 6;\n    }\n    function hufBuildDecTable(hcode, im, iM, hdecod) {\n      for (; im <= iM; im++) {\n        var c = hufCode(hcode[im]);\n        var l = hufLength(hcode[im]);\n        if (c >> l) {\n          throw \"Invalid table entry\";\n        }\n        if (l > HUF_DECBITS) {\n          var pl = hdecod[c >> l - HUF_DECBITS];\n          if (pl.len) {\n            throw \"Invalid table entry\";\n          }\n          pl.lit++;\n          if (pl.p) {\n            var p = pl.p;\n            pl.p = new Array(pl.lit);\n            for (var i = 0; i < pl.lit - 1; ++i) {\n              pl.p[i] = p[i];\n            }\n          } else {\n            pl.p = new Array(1);\n          }\n          pl.p[pl.lit - 1] = im;\n        } else if (l) {\n          var plOffset = 0;\n          for (var i = 1 << HUF_DECBITS - l; i > 0; i--) {\n            var pl = hdecod[(c << HUF_DECBITS - l) + plOffset];\n            if (pl.len || pl.p) {\n              throw \"Invalid table entry\";\n            }\n            pl.len = l;\n            pl.lit = im;\n            plOffset++;\n          }\n        }\n      }\n      return true;\n    }\n    const getCharReturn = { c: 0, lc: 0 };\n    function getChar(c, lc, uInt8Array2, inOffset) {\n      c = c << 8 | parseUint8Array(uInt8Array2, inOffset);\n      lc += 8;\n      getCharReturn.c = c;\n      getCharReturn.lc = lc;\n    }\n    const getCodeReturn = { c: 0, lc: 0 };\n    function getCode(po, rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outBufferOffset, outBufferEndOffset) {\n      if (po == rlc) {\n        if (lc < 8) {\n          getChar(c, lc, uInt8Array2, inOffset);\n          c = getCharReturn.c;\n          lc = getCharReturn.lc;\n        }\n        lc -= 8;\n        var cs = c >> lc;\n        var cs = new Uint8Array([cs])[0];\n        if (outBufferOffset.value + cs > outBufferEndOffset) {\n          return false;\n        }\n        var s = outBuffer[outBufferOffset.value - 1];\n        while (cs-- > 0) {\n          outBuffer[outBufferOffset.value++] = s;\n        }\n      } else if (outBufferOffset.value < outBufferEndOffset) {\n        outBuffer[outBufferOffset.value++] = po;\n      } else {\n        return false;\n      }\n      getCodeReturn.c = c;\n      getCodeReturn.lc = lc;\n    }\n    function UInt16(value) {\n      return value & 65535;\n    }\n    function Int16(value) {\n      var ref = UInt16(value);\n      return ref > 32767 ? ref - 65536 : ref;\n    }\n    const wdec14Return = { a: 0, b: 0 };\n    function wdec14(l, h) {\n      var ls = Int16(l);\n      var hs = Int16(h);\n      var hi = hs;\n      var ai = ls + (hi & 1) + (hi >> 1);\n      var as = ai;\n      var bs = ai - hi;\n      wdec14Return.a = as;\n      wdec14Return.b = bs;\n    }\n    function wdec16(l, h) {\n      var m = UInt16(l);\n      var d = UInt16(h);\n      var bb = m - (d >> 1) & MOD_MASK;\n      var aa = d + bb - A_OFFSET & MOD_MASK;\n      wdec14Return.a = aa;\n      wdec14Return.b = bb;\n    }\n    function wav2Decode(buffer2, j, nx, ox, ny, oy, mx) {\n      var w14 = mx < 1 << 14;\n      var n = nx > ny ? ny : nx;\n      var p = 1;\n      var p2;\n      while (p <= n)\n        p <<= 1;\n      p >>= 1;\n      p2 = p;\n      p >>= 1;\n      while (p >= 1) {\n        var py = 0;\n        var ey = py + oy * (ny - p2);\n        var oy1 = oy * p;\n        var oy2 = oy * p2;\n        var ox1 = ox * p;\n        var ox2 = ox * p2;\n        var i00, i01, i10, i11;\n        for (; py <= ey; py += oy2) {\n          var px = py;\n          var ex = py + ox * (nx - p2);\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1;\n            var p10 = px + oy1;\n            var p11 = p10 + ox1;\n            if (w14) {\n              wdec14(buffer2[px + j], buffer2[p10 + j]);\n              i00 = wdec14Return.a;\n              i10 = wdec14Return.b;\n              wdec14(buffer2[p01 + j], buffer2[p11 + j]);\n              i01 = wdec14Return.a;\n              i11 = wdec14Return.b;\n              wdec14(i00, i01);\n              buffer2[px + j] = wdec14Return.a;\n              buffer2[p01 + j] = wdec14Return.b;\n              wdec14(i10, i11);\n              buffer2[p10 + j] = wdec14Return.a;\n              buffer2[p11 + j] = wdec14Return.b;\n            } else {\n              wdec16(buffer2[px + j], buffer2[p10 + j]);\n              i00 = wdec14Return.a;\n              i10 = wdec14Return.b;\n              wdec16(buffer2[p01 + j], buffer2[p11 + j]);\n              i01 = wdec14Return.a;\n              i11 = wdec14Return.b;\n              wdec16(i00, i01);\n              buffer2[px + j] = wdec14Return.a;\n              buffer2[p01 + j] = wdec14Return.b;\n              wdec16(i10, i11);\n              buffer2[p10 + j] = wdec14Return.a;\n              buffer2[p11 + j] = wdec14Return.b;\n            }\n          }\n          if (nx & p) {\n            var p10 = px + oy1;\n            if (w14)\n              wdec14(buffer2[px + j], buffer2[p10 + j]);\n            else\n              wdec16(buffer2[px + j], buffer2[p10 + j]);\n            i00 = wdec14Return.a;\n            buffer2[p10 + j] = wdec14Return.b;\n            buffer2[px + j] = i00;\n          }\n        }\n        if (ny & p) {\n          var px = py;\n          var ex = py + ox * (nx - p2);\n          for (; px <= ex; px += ox2) {\n            var p01 = px + ox1;\n            if (w14)\n              wdec14(buffer2[px + j], buffer2[p01 + j]);\n            else\n              wdec16(buffer2[px + j], buffer2[p01 + j]);\n            i00 = wdec14Return.a;\n            buffer2[p01 + j] = wdec14Return.b;\n            buffer2[px + j] = i00;\n          }\n        }\n        p2 = p;\n        p >>= 1;\n      }\n      return py;\n    }\n    function hufDecode(encodingTable, decodingTable, uInt8Array2, inDataView, inOffset, ni, rlc, no, outBuffer, outOffset) {\n      var c = 0;\n      var lc = 0;\n      var outBufferEndOffset = no;\n      var inOffsetEnd = Math.trunc(inOffset.value + (ni + 7) / 8);\n      while (inOffset.value < inOffsetEnd) {\n        getChar(c, lc, uInt8Array2, inOffset);\n        c = getCharReturn.c;\n        lc = getCharReturn.lc;\n        while (lc >= HUF_DECBITS) {\n          var index = c >> lc - HUF_DECBITS & HUF_DECMASK;\n          var pl = decodingTable[index];\n          if (pl.len) {\n            lc -= pl.len;\n            getCode(pl.lit, rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset);\n            c = getCodeReturn.c;\n            lc = getCodeReturn.lc;\n          } else {\n            if (!pl.p) {\n              throw \"hufDecode issues\";\n            }\n            var j;\n            for (j = 0; j < pl.lit; j++) {\n              var l = hufLength(encodingTable[pl.p[j]]);\n              while (lc < l && inOffset.value < inOffsetEnd) {\n                getChar(c, lc, uInt8Array2, inOffset);\n                c = getCharReturn.c;\n                lc = getCharReturn.lc;\n              }\n              if (lc >= l) {\n                if (hufCode(encodingTable[pl.p[j]]) == (c >> lc - l & (1 << l) - 1)) {\n                  lc -= l;\n                  getCode(\n                    pl.p[j],\n                    rlc,\n                    c,\n                    lc,\n                    uInt8Array2,\n                    inDataView,\n                    inOffset,\n                    outBuffer,\n                    outOffset,\n                    outBufferEndOffset\n                  );\n                  c = getCodeReturn.c;\n                  lc = getCodeReturn.lc;\n                  break;\n                }\n              }\n            }\n            if (j == pl.lit) {\n              throw \"hufDecode issues\";\n            }\n          }\n        }\n      }\n      var i = 8 - ni & 7;\n      c >>= i;\n      lc -= i;\n      while (lc > 0) {\n        var pl = decodingTable[c << HUF_DECBITS - lc & HUF_DECMASK];\n        if (pl.len) {\n          lc -= pl.len;\n          getCode(pl.lit, rlc, c, lc, uInt8Array2, inDataView, inOffset, outBuffer, outOffset, outBufferEndOffset);\n          c = getCodeReturn.c;\n          lc = getCodeReturn.lc;\n        } else {\n          throw \"hufDecode issues\";\n        }\n      }\n      return true;\n    }\n    function hufUncompress(uInt8Array2, inDataView, inOffset, nCompressed, outBuffer, nRaw) {\n      var outOffset = { value: 0 };\n      var initialInOffset = inOffset.value;\n      var im = parseUint32(inDataView, inOffset);\n      var iM = parseUint32(inDataView, inOffset);\n      inOffset.value += 4;\n      var nBits = parseUint32(inDataView, inOffset);\n      inOffset.value += 4;\n      if (im < 0 || im >= HUF_ENCSIZE || iM < 0 || iM >= HUF_ENCSIZE) {\n        throw \"Something wrong with HUF_ENCSIZE\";\n      }\n      var freq = new Array(HUF_ENCSIZE);\n      var hdec = new Array(HUF_DECSIZE);\n      hufClearDecTable(hdec);\n      var ni = nCompressed - (inOffset.value - initialInOffset);\n      hufUnpackEncTable(uInt8Array2, inDataView, inOffset, ni, im, iM, freq);\n      if (nBits > 8 * (nCompressed - (inOffset.value - initialInOffset))) {\n        throw \"Something wrong with hufUncompress\";\n      }\n      hufBuildDecTable(freq, im, iM, hdec);\n      hufDecode(freq, hdec, uInt8Array2, inDataView, inOffset, nBits, iM, nRaw, outBuffer, outOffset);\n    }\n    function applyLut(lut, data, nData) {\n      for (var i = 0; i < nData; ++i) {\n        data[i] = lut[data[i]];\n      }\n    }\n    function predictor(source) {\n      for (var t = 1; t < source.length; t++) {\n        var d = source[t - 1] + source[t] - 128;\n        source[t] = d;\n      }\n    }\n    function interleaveScalar(source, out) {\n      var t1 = 0;\n      var t2 = Math.floor((source.length + 1) / 2);\n      var s = 0;\n      var stop = source.length - 1;\n      while (true) {\n        if (s > stop)\n          break;\n        out[s++] = source[t1++];\n        if (s > stop)\n          break;\n        out[s++] = source[t2++];\n      }\n    }\n    function decodeRunLength(source) {\n      var size = source.byteLength;\n      var out = new Array();\n      var p = 0;\n      var reader = new DataView(source);\n      while (size > 0) {\n        var l = reader.getInt8(p++);\n        if (l < 0) {\n          var count = -l;\n          size -= count + 1;\n          for (var i = 0; i < count; i++) {\n            out.push(reader.getUint8(p++));\n          }\n        } else {\n          var count = l;\n          size -= 2;\n          var value = reader.getUint8(p++);\n          for (var i = 0; i < count + 1; i++) {\n            out.push(value);\n          }\n        }\n      }\n      return out;\n    }\n    function lossyDctDecode(cscSet, rowPtrs, channelData, acBuffer, dcBuffer, outBuffer) {\n      var dataView = new DataView(outBuffer.buffer);\n      var width = channelData[cscSet.idx[0]].width;\n      var height = channelData[cscSet.idx[0]].height;\n      var numComp = 3;\n      var numFullBlocksX = Math.floor(width / 8);\n      var numBlocksX = Math.ceil(width / 8);\n      var numBlocksY = Math.ceil(height / 8);\n      var leftoverX = width - (numBlocksX - 1) * 8;\n      var leftoverY = height - (numBlocksY - 1) * 8;\n      var currAcComp = { value: 0 };\n      var currDcComp = new Array(numComp);\n      var dctData = new Array(numComp);\n      var halfZigBlock = new Array(numComp);\n      var rowBlock = new Array(numComp);\n      var rowOffsets = new Array(numComp);\n      for (let comp2 = 0; comp2 < numComp; ++comp2) {\n        rowOffsets[comp2] = rowPtrs[cscSet.idx[comp2]];\n        currDcComp[comp2] = comp2 < 1 ? 0 : currDcComp[comp2 - 1] + numBlocksX * numBlocksY;\n        dctData[comp2] = new Float32Array(64);\n        halfZigBlock[comp2] = new Uint16Array(64);\n        rowBlock[comp2] = new Uint16Array(numBlocksX * 64);\n      }\n      for (let blocky = 0; blocky < numBlocksY; ++blocky) {\n        var maxY = 8;\n        if (blocky == numBlocksY - 1)\n          maxY = leftoverY;\n        var maxX = 8;\n        for (let blockx = 0; blockx < numBlocksX; ++blockx) {\n          if (blockx == numBlocksX - 1)\n            maxX = leftoverX;\n          for (let comp2 = 0; comp2 < numComp; ++comp2) {\n            halfZigBlock[comp2].fill(0);\n            halfZigBlock[comp2][0] = dcBuffer[currDcComp[comp2]++];\n            unRleAC(currAcComp, acBuffer, halfZigBlock[comp2]);\n            unZigZag(halfZigBlock[comp2], dctData[comp2]);\n            dctInverse(dctData[comp2]);\n          }\n          {\n            csc709Inverse(dctData);\n          }\n          for (let comp2 = 0; comp2 < numComp; ++comp2) {\n            convertToHalf(dctData[comp2], rowBlock[comp2], blockx * 64);\n          }\n        }\n        let offset2 = 0;\n        for (let comp2 = 0; comp2 < numComp; ++comp2) {\n          const type2 = channelData[cscSet.idx[comp2]].type;\n          for (let y2 = 8 * blocky; y2 < 8 * blocky + maxY; ++y2) {\n            offset2 = rowOffsets[comp2][y2];\n            for (let blockx = 0; blockx < numFullBlocksX; ++blockx) {\n              const src = blockx * 64 + (y2 & 7) * 8;\n              dataView.setUint16(offset2 + 0 * INT16_SIZE * type2, rowBlock[comp2][src + 0], true);\n              dataView.setUint16(offset2 + 1 * INT16_SIZE * type2, rowBlock[comp2][src + 1], true);\n              dataView.setUint16(offset2 + 2 * INT16_SIZE * type2, rowBlock[comp2][src + 2], true);\n              dataView.setUint16(offset2 + 3 * INT16_SIZE * type2, rowBlock[comp2][src + 3], true);\n              dataView.setUint16(offset2 + 4 * INT16_SIZE * type2, rowBlock[comp2][src + 4], true);\n              dataView.setUint16(offset2 + 5 * INT16_SIZE * type2, rowBlock[comp2][src + 5], true);\n              dataView.setUint16(offset2 + 6 * INT16_SIZE * type2, rowBlock[comp2][src + 6], true);\n              dataView.setUint16(offset2 + 7 * INT16_SIZE * type2, rowBlock[comp2][src + 7], true);\n              offset2 += 8 * INT16_SIZE * type2;\n            }\n          }\n          if (numFullBlocksX != numBlocksX) {\n            for (let y2 = 8 * blocky; y2 < 8 * blocky + maxY; ++y2) {\n              const offset3 = rowOffsets[comp2][y2] + 8 * numFullBlocksX * INT16_SIZE * type2;\n              const src = numFullBlocksX * 64 + (y2 & 7) * 8;\n              for (let x2 = 0; x2 < maxX; ++x2) {\n                dataView.setUint16(offset3 + x2 * INT16_SIZE * type2, rowBlock[comp2][src + x2], true);\n              }\n            }\n          }\n        }\n      }\n      var halfRow = new Uint16Array(width);\n      var dataView = new DataView(outBuffer.buffer);\n      for (var comp = 0; comp < numComp; ++comp) {\n        channelData[cscSet.idx[comp]].decoded = true;\n        var type = channelData[cscSet.idx[comp]].type;\n        if (channelData[comp].type != 2)\n          continue;\n        for (var y = 0; y < height; ++y) {\n          const offset2 = rowOffsets[comp][y];\n          for (var x = 0; x < width; ++x) {\n            halfRow[x] = dataView.getUint16(offset2 + x * INT16_SIZE * type, true);\n          }\n          for (var x = 0; x < width; ++x) {\n            dataView.setFloat32(offset2 + x * INT16_SIZE * type, decodeFloat16(halfRow[x]), true);\n          }\n        }\n      }\n    }\n    function unRleAC(currAcComp, acBuffer, halfZigBlock) {\n      var acValue;\n      var dctComp = 1;\n      while (dctComp < 64) {\n        acValue = acBuffer[currAcComp.value];\n        if (acValue == 65280) {\n          dctComp = 64;\n        } else if (acValue >> 8 == 255) {\n          dctComp += acValue & 255;\n        } else {\n          halfZigBlock[dctComp] = acValue;\n          dctComp++;\n        }\n        currAcComp.value++;\n      }\n    }\n    function unZigZag(src, dst) {\n      dst[0] = decodeFloat16(src[0]);\n      dst[1] = decodeFloat16(src[1]);\n      dst[2] = decodeFloat16(src[5]);\n      dst[3] = decodeFloat16(src[6]);\n      dst[4] = decodeFloat16(src[14]);\n      dst[5] = decodeFloat16(src[15]);\n      dst[6] = decodeFloat16(src[27]);\n      dst[7] = decodeFloat16(src[28]);\n      dst[8] = decodeFloat16(src[2]);\n      dst[9] = decodeFloat16(src[4]);\n      dst[10] = decodeFloat16(src[7]);\n      dst[11] = decodeFloat16(src[13]);\n      dst[12] = decodeFloat16(src[16]);\n      dst[13] = decodeFloat16(src[26]);\n      dst[14] = decodeFloat16(src[29]);\n      dst[15] = decodeFloat16(src[42]);\n      dst[16] = decodeFloat16(src[3]);\n      dst[17] = decodeFloat16(src[8]);\n      dst[18] = decodeFloat16(src[12]);\n      dst[19] = decodeFloat16(src[17]);\n      dst[20] = decodeFloat16(src[25]);\n      dst[21] = decodeFloat16(src[30]);\n      dst[22] = decodeFloat16(src[41]);\n      dst[23] = decodeFloat16(src[43]);\n      dst[24] = decodeFloat16(src[9]);\n      dst[25] = decodeFloat16(src[11]);\n      dst[26] = decodeFloat16(src[18]);\n      dst[27] = decodeFloat16(src[24]);\n      dst[28] = decodeFloat16(src[31]);\n      dst[29] = decodeFloat16(src[40]);\n      dst[30] = decodeFloat16(src[44]);\n      dst[31] = decodeFloat16(src[53]);\n      dst[32] = decodeFloat16(src[10]);\n      dst[33] = decodeFloat16(src[19]);\n      dst[34] = decodeFloat16(src[23]);\n      dst[35] = decodeFloat16(src[32]);\n      dst[36] = decodeFloat16(src[39]);\n      dst[37] = decodeFloat16(src[45]);\n      dst[38] = decodeFloat16(src[52]);\n      dst[39] = decodeFloat16(src[54]);\n      dst[40] = decodeFloat16(src[20]);\n      dst[41] = decodeFloat16(src[22]);\n      dst[42] = decodeFloat16(src[33]);\n      dst[43] = decodeFloat16(src[38]);\n      dst[44] = decodeFloat16(src[46]);\n      dst[45] = decodeFloat16(src[51]);\n      dst[46] = decodeFloat16(src[55]);\n      dst[47] = decodeFloat16(src[60]);\n      dst[48] = decodeFloat16(src[21]);\n      dst[49] = decodeFloat16(src[34]);\n      dst[50] = decodeFloat16(src[37]);\n      dst[51] = decodeFloat16(src[47]);\n      dst[52] = decodeFloat16(src[50]);\n      dst[53] = decodeFloat16(src[56]);\n      dst[54] = decodeFloat16(src[59]);\n      dst[55] = decodeFloat16(src[61]);\n      dst[56] = decodeFloat16(src[35]);\n      dst[57] = decodeFloat16(src[36]);\n      dst[58] = decodeFloat16(src[48]);\n      dst[59] = decodeFloat16(src[49]);\n      dst[60] = decodeFloat16(src[57]);\n      dst[61] = decodeFloat16(src[58]);\n      dst[62] = decodeFloat16(src[62]);\n      dst[63] = decodeFloat16(src[63]);\n    }\n    function dctInverse(data) {\n      const a = 0.5 * Math.cos(3.14159 / 4);\n      const b = 0.5 * Math.cos(3.14159 / 16);\n      const c = 0.5 * Math.cos(3.14159 / 8);\n      const d = 0.5 * Math.cos(3 * 3.14159 / 16);\n      const e = 0.5 * Math.cos(5 * 3.14159 / 16);\n      const f = 0.5 * Math.cos(3 * 3.14159 / 8);\n      const g = 0.5 * Math.cos(7 * 3.14159 / 16);\n      var alpha = new Array(4);\n      var beta = new Array(4);\n      var theta = new Array(4);\n      var gamma = new Array(4);\n      for (var row = 0; row < 8; ++row) {\n        var rowPtr = row * 8;\n        alpha[0] = c * data[rowPtr + 2];\n        alpha[1] = f * data[rowPtr + 2];\n        alpha[2] = c * data[rowPtr + 6];\n        alpha[3] = f * data[rowPtr + 6];\n        beta[0] = b * data[rowPtr + 1] + d * data[rowPtr + 3] + e * data[rowPtr + 5] + g * data[rowPtr + 7];\n        beta[1] = d * data[rowPtr + 1] - g * data[rowPtr + 3] - b * data[rowPtr + 5] - e * data[rowPtr + 7];\n        beta[2] = e * data[rowPtr + 1] - b * data[rowPtr + 3] + g * data[rowPtr + 5] + d * data[rowPtr + 7];\n        beta[3] = g * data[rowPtr + 1] - e * data[rowPtr + 3] + d * data[rowPtr + 5] - b * data[rowPtr + 7];\n        theta[0] = a * (data[rowPtr + 0] + data[rowPtr + 4]);\n        theta[3] = a * (data[rowPtr + 0] - data[rowPtr + 4]);\n        theta[1] = alpha[0] + alpha[3];\n        theta[2] = alpha[1] - alpha[2];\n        gamma[0] = theta[0] + theta[1];\n        gamma[1] = theta[3] + theta[2];\n        gamma[2] = theta[3] - theta[2];\n        gamma[3] = theta[0] - theta[1];\n        data[rowPtr + 0] = gamma[0] + beta[0];\n        data[rowPtr + 1] = gamma[1] + beta[1];\n        data[rowPtr + 2] = gamma[2] + beta[2];\n        data[rowPtr + 3] = gamma[3] + beta[3];\n        data[rowPtr + 4] = gamma[3] - beta[3];\n        data[rowPtr + 5] = gamma[2] - beta[2];\n        data[rowPtr + 6] = gamma[1] - beta[1];\n        data[rowPtr + 7] = gamma[0] - beta[0];\n      }\n      for (var column = 0; column < 8; ++column) {\n        alpha[0] = c * data[16 + column];\n        alpha[1] = f * data[16 + column];\n        alpha[2] = c * data[48 + column];\n        alpha[3] = f * data[48 + column];\n        beta[0] = b * data[8 + column] + d * data[24 + column] + e * data[40 + column] + g * data[56 + column];\n        beta[1] = d * data[8 + column] - g * data[24 + column] - b * data[40 + column] - e * data[56 + column];\n        beta[2] = e * data[8 + column] - b * data[24 + column] + g * data[40 + column] + d * data[56 + column];\n        beta[3] = g * data[8 + column] - e * data[24 + column] + d * data[40 + column] - b * data[56 + column];\n        theta[0] = a * (data[column] + data[32 + column]);\n        theta[3] = a * (data[column] - data[32 + column]);\n        theta[1] = alpha[0] + alpha[3];\n        theta[2] = alpha[1] - alpha[2];\n        gamma[0] = theta[0] + theta[1];\n        gamma[1] = theta[3] + theta[2];\n        gamma[2] = theta[3] - theta[2];\n        gamma[3] = theta[0] - theta[1];\n        data[0 + column] = gamma[0] + beta[0];\n        data[8 + column] = gamma[1] + beta[1];\n        data[16 + column] = gamma[2] + beta[2];\n        data[24 + column] = gamma[3] + beta[3];\n        data[32 + column] = gamma[3] - beta[3];\n        data[40 + column] = gamma[2] - beta[2];\n        data[48 + column] = gamma[1] - beta[1];\n        data[56 + column] = gamma[0] - beta[0];\n      }\n    }\n    function csc709Inverse(data) {\n      for (var i = 0; i < 64; ++i) {\n        var y = data[0][i];\n        var cb = data[1][i];\n        var cr = data[2][i];\n        data[0][i] = y + 1.5747 * cr;\n        data[1][i] = y - 0.1873 * cb - 0.4682 * cr;\n        data[2][i] = y + 1.8556 * cb;\n      }\n    }\n    function convertToHalf(src, dst, idx) {\n      for (var i = 0; i < 64; ++i) {\n        dst[idx + i] = three__WEBPACK_IMPORTED_MODULE_1__.DataUtils.toHalfFloat(toLinear(src[i]));\n      }\n    }\n    function toLinear(float) {\n      if (float <= 1) {\n        return Math.sign(float) * Math.pow(Math.abs(float), 2.2);\n      } else {\n        return Math.sign(float) * Math.pow(logBase, Math.abs(float) - 1);\n      }\n    }\n    function uncompressRAW(info) {\n      return new DataView(info.array.buffer, info.offset.value, info.size);\n    }\n    function uncompressRLE(info) {\n      var compressed = info.viewer.buffer.slice(info.offset.value, info.offset.value + info.size);\n      var rawBuffer = new Uint8Array(decodeRunLength(compressed));\n      var tmpBuffer = new Uint8Array(rawBuffer.length);\n      predictor(rawBuffer);\n      interleaveScalar(rawBuffer, tmpBuffer);\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressZIP(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size);\n      var rawBuffer = (0,fflate__WEBPACK_IMPORTED_MODULE_2__.unzlibSync)(compressed);\n      var tmpBuffer = new Uint8Array(rawBuffer.length);\n      predictor(rawBuffer);\n      interleaveScalar(rawBuffer, tmpBuffer);\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressPIZ(info) {\n      var inDataView = info.viewer;\n      var inOffset = { value: info.offset.value };\n      var outBuffer = new Uint16Array(info.width * info.scanlineBlockSize * (info.channels * info.type));\n      var bitmap = new Uint8Array(BITMAP_SIZE);\n      var outBufferEnd = 0;\n      var pizChannelData = new Array(info.channels);\n      for (var i = 0; i < info.channels; i++) {\n        pizChannelData[i] = {};\n        pizChannelData[i][\"start\"] = outBufferEnd;\n        pizChannelData[i][\"end\"] = pizChannelData[i][\"start\"];\n        pizChannelData[i][\"nx\"] = info.width;\n        pizChannelData[i][\"ny\"] = info.lines;\n        pizChannelData[i][\"size\"] = info.type;\n        outBufferEnd += pizChannelData[i].nx * pizChannelData[i].ny * pizChannelData[i].size;\n      }\n      var minNonZero = parseUint16(inDataView, inOffset);\n      var maxNonZero = parseUint16(inDataView, inOffset);\n      if (maxNonZero >= BITMAP_SIZE) {\n        throw \"Something is wrong with PIZ_COMPRESSION BITMAP_SIZE\";\n      }\n      if (minNonZero <= maxNonZero) {\n        for (var i = 0; i < maxNonZero - minNonZero + 1; i++) {\n          bitmap[i + minNonZero] = parseUint8(inDataView, inOffset);\n        }\n      }\n      var lut = new Uint16Array(USHORT_RANGE);\n      var maxValue = reverseLutFromBitmap(bitmap, lut);\n      var length = parseUint32(inDataView, inOffset);\n      hufUncompress(info.array, inDataView, inOffset, length, outBuffer, outBufferEnd);\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = pizChannelData[i];\n        for (var j = 0; j < pizChannelData[i].size; ++j) {\n          wav2Decode(outBuffer, cd.start + j, cd.nx, cd.size, cd.ny, cd.nx * cd.size, maxValue);\n        }\n      }\n      applyLut(lut, outBuffer, outBufferEnd);\n      var tmpOffset2 = 0;\n      var tmpBuffer = new Uint8Array(outBuffer.buffer.byteLength);\n      for (var y = 0; y < info.lines; y++) {\n        for (var c = 0; c < info.channels; c++) {\n          var cd = pizChannelData[c];\n          var n = cd.nx * cd.size;\n          var cp = new Uint8Array(outBuffer.buffer, cd.end * INT16_SIZE, n * INT16_SIZE);\n          tmpBuffer.set(cp, tmpOffset2);\n          tmpOffset2 += n * INT16_SIZE;\n          cd.end += n;\n        }\n      }\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressPXR(info) {\n      var compressed = info.array.slice(info.offset.value, info.offset.value + info.size);\n      var rawBuffer = (0,fflate__WEBPACK_IMPORTED_MODULE_2__.unzlibSync)(compressed);\n      const sz = info.lines * info.channels * info.width;\n      const tmpBuffer = info.type == 1 ? new Uint16Array(sz) : new Uint32Array(sz);\n      let tmpBufferEnd = 0;\n      let writePtr = 0;\n      const ptr = new Array(4);\n      for (let y = 0; y < info.lines; y++) {\n        for (let c = 0; c < info.channels; c++) {\n          let pixel = 0;\n          switch (info.type) {\n            case 1:\n              ptr[0] = tmpBufferEnd;\n              ptr[1] = ptr[0] + info.width;\n              tmpBufferEnd = ptr[1] + info.width;\n              for (let j = 0; j < info.width; ++j) {\n                const diff = rawBuffer[ptr[0]++] << 8 | rawBuffer[ptr[1]++];\n                pixel += diff;\n                tmpBuffer[writePtr] = pixel;\n                writePtr++;\n              }\n              break;\n            case 2:\n              ptr[0] = tmpBufferEnd;\n              ptr[1] = ptr[0] + info.width;\n              ptr[2] = ptr[1] + info.width;\n              tmpBufferEnd = ptr[2] + info.width;\n              for (let j = 0; j < info.width; ++j) {\n                const diff = rawBuffer[ptr[0]++] << 24 | rawBuffer[ptr[1]++] << 16 | rawBuffer[ptr[2]++] << 8;\n                pixel += diff;\n                tmpBuffer[writePtr] = pixel;\n                writePtr++;\n              }\n              break;\n          }\n        }\n      }\n      return new DataView(tmpBuffer.buffer);\n    }\n    function uncompressDWA(info) {\n      var inDataView = info.viewer;\n      var inOffset = { value: info.offset.value };\n      var outBuffer = new Uint8Array(info.width * info.lines * (info.channels * info.type * INT16_SIZE));\n      var dwaHeader = {\n        version: parseInt64(inDataView, inOffset),\n        unknownUncompressedSize: parseInt64(inDataView, inOffset),\n        unknownCompressedSize: parseInt64(inDataView, inOffset),\n        acCompressedSize: parseInt64(inDataView, inOffset),\n        dcCompressedSize: parseInt64(inDataView, inOffset),\n        rleCompressedSize: parseInt64(inDataView, inOffset),\n        rleUncompressedSize: parseInt64(inDataView, inOffset),\n        rleRawSize: parseInt64(inDataView, inOffset),\n        totalAcUncompressedCount: parseInt64(inDataView, inOffset),\n        totalDcUncompressedCount: parseInt64(inDataView, inOffset),\n        acCompression: parseInt64(inDataView, inOffset)\n      };\n      if (dwaHeader.version < 2) {\n        throw \"EXRLoader.parse: \" + EXRHeader.compression + \" version \" + dwaHeader.version + \" is unsupported\";\n      }\n      var channelRules = new Array();\n      var ruleSize = parseUint16(inDataView, inOffset) - INT16_SIZE;\n      while (ruleSize > 0) {\n        var name = parseNullTerminatedString(inDataView.buffer, inOffset);\n        var value = parseUint8(inDataView, inOffset);\n        var compression = value >> 2 & 3;\n        var csc = (value >> 4) - 1;\n        var index = new Int8Array([csc])[0];\n        var type = parseUint8(inDataView, inOffset);\n        channelRules.push({\n          name,\n          index,\n          type,\n          compression\n        });\n        ruleSize -= name.length + 3;\n      }\n      var channels = EXRHeader.channels;\n      var channelData = new Array(info.channels);\n      for (var i = 0; i < info.channels; ++i) {\n        var cd = channelData[i] = {};\n        var channel = channels[i];\n        cd.name = channel.name;\n        cd.compression = UNKNOWN;\n        cd.decoded = false;\n        cd.type = channel.pixelType;\n        cd.pLinear = channel.pLinear;\n        cd.width = info.width;\n        cd.height = info.lines;\n      }\n      var cscSet = {\n        idx: new Array(3)\n      };\n      for (var offset2 = 0; offset2 < info.channels; ++offset2) {\n        var cd = channelData[offset2];\n        for (var i = 0; i < channelRules.length; ++i) {\n          var rule = channelRules[i];\n          if (cd.name == rule.name) {\n            cd.compression = rule.compression;\n            if (rule.index >= 0) {\n              cscSet.idx[rule.index] = offset2;\n            }\n            cd.offset = offset2;\n          }\n        }\n      }\n      if (dwaHeader.acCompressedSize > 0) {\n        switch (dwaHeader.acCompression) {\n          case STATIC_HUFFMAN:\n            var acBuffer = new Uint16Array(dwaHeader.totalAcUncompressedCount);\n            hufUncompress(\n              info.array,\n              inDataView,\n              inOffset,\n              dwaHeader.acCompressedSize,\n              acBuffer,\n              dwaHeader.totalAcUncompressedCount\n            );\n            break;\n          case DEFLATE:\n            var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.totalAcUncompressedCount);\n            var data = (0,fflate__WEBPACK_IMPORTED_MODULE_2__.unzlibSync)(compressed);\n            var acBuffer = new Uint16Array(data.buffer);\n            inOffset.value += dwaHeader.totalAcUncompressedCount;\n            break;\n        }\n      }\n      if (dwaHeader.dcCompressedSize > 0) {\n        var zlibInfo = {\n          array: info.array,\n          offset: inOffset,\n          size: dwaHeader.dcCompressedSize\n        };\n        var dcBuffer = new Uint16Array(uncompressZIP(zlibInfo).buffer);\n        inOffset.value += dwaHeader.dcCompressedSize;\n      }\n      if (dwaHeader.rleRawSize > 0) {\n        var compressed = info.array.slice(inOffset.value, inOffset.value + dwaHeader.rleCompressedSize);\n        var data = (0,fflate__WEBPACK_IMPORTED_MODULE_2__.unzlibSync)(compressed);\n        var rleBuffer = decodeRunLength(data.buffer);\n        inOffset.value += dwaHeader.rleCompressedSize;\n      }\n      var outBufferEnd = 0;\n      var rowOffsets = new Array(channelData.length);\n      for (var i = 0; i < rowOffsets.length; ++i) {\n        rowOffsets[i] = new Array();\n      }\n      for (var y = 0; y < info.lines; ++y) {\n        for (var chan = 0; chan < channelData.length; ++chan) {\n          rowOffsets[chan].push(outBufferEnd);\n          outBufferEnd += channelData[chan].width * info.type * INT16_SIZE;\n        }\n      }\n      lossyDctDecode(cscSet, rowOffsets, channelData, acBuffer, dcBuffer, outBuffer);\n      for (var i = 0; i < channelData.length; ++i) {\n        var cd = channelData[i];\n        if (cd.decoded)\n          continue;\n        switch (cd.compression) {\n          case RLE:\n            var row = 0;\n            var rleOffset = 0;\n            for (var y = 0; y < info.lines; ++y) {\n              var rowOffsetBytes = rowOffsets[i][row];\n              for (var x = 0; x < cd.width; ++x) {\n                for (var byte = 0; byte < INT16_SIZE * cd.type; ++byte) {\n                  outBuffer[rowOffsetBytes++] = rleBuffer[rleOffset + byte * cd.width * cd.height];\n                }\n                rleOffset++;\n              }\n              row++;\n            }\n            break;\n          case LOSSY_DCT:\n          default:\n            throw \"EXRLoader.parse: unsupported channel compression\";\n        }\n      }\n      return new DataView(outBuffer.buffer);\n    }\n    function parseNullTerminatedString(buffer2, offset2) {\n      var uintBuffer = new Uint8Array(buffer2);\n      var endOffset = 0;\n      while (uintBuffer[offset2.value + endOffset] != 0) {\n        endOffset += 1;\n      }\n      var stringValue = new TextDecoder().decode(uintBuffer.slice(offset2.value, offset2.value + endOffset));\n      offset2.value = offset2.value + endOffset + 1;\n      return stringValue;\n    }\n    function parseFixedLengthString(buffer2, offset2, size) {\n      var stringValue = new TextDecoder().decode(new Uint8Array(buffer2).slice(offset2.value, offset2.value + size));\n      offset2.value = offset2.value + size;\n      return stringValue;\n    }\n    function parseRational(dataView, offset2) {\n      var x = parseInt32(dataView, offset2);\n      var y = parseUint32(dataView, offset2);\n      return [x, y];\n    }\n    function parseTimecode(dataView, offset2) {\n      var x = parseUint32(dataView, offset2);\n      var y = parseUint32(dataView, offset2);\n      return [x, y];\n    }\n    function parseInt32(dataView, offset2) {\n      var Int32 = dataView.getInt32(offset2.value, true);\n      offset2.value = offset2.value + INT32_SIZE;\n      return Int32;\n    }\n    function parseUint32(dataView, offset2) {\n      var Uint32 = dataView.getUint32(offset2.value, true);\n      offset2.value = offset2.value + INT32_SIZE;\n      return Uint32;\n    }\n    function parseUint8Array(uInt8Array2, offset2) {\n      var Uint8 = uInt8Array2[offset2.value];\n      offset2.value = offset2.value + INT8_SIZE;\n      return Uint8;\n    }\n    function parseUint8(dataView, offset2) {\n      var Uint8 = dataView.getUint8(offset2.value);\n      offset2.value = offset2.value + INT8_SIZE;\n      return Uint8;\n    }\n    const parseInt64 = function(dataView, offset2) {\n      let int;\n      if (\"getBigInt64\" in DataView.prototype) {\n        int = Number(dataView.getBigInt64(offset2.value, true));\n      } else {\n        int = dataView.getUint32(offset2.value + 4, true) + Number(dataView.getUint32(offset2.value, true) << 32);\n      }\n      offset2.value += ULONG_SIZE;\n      return int;\n    };\n    function parseFloat32(dataView, offset2) {\n      var float = dataView.getFloat32(offset2.value, true);\n      offset2.value += FLOAT32_SIZE;\n      return float;\n    }\n    function decodeFloat32(dataView, offset2) {\n      return three__WEBPACK_IMPORTED_MODULE_1__.DataUtils.toHalfFloat(parseFloat32(dataView, offset2));\n    }\n    function decodeFloat16(binary) {\n      var exponent = (binary & 31744) >> 10, fraction = binary & 1023;\n      return (binary >> 15 ? -1 : 1) * (exponent ? exponent === 31 ? fraction ? NaN : Infinity : Math.pow(2, exponent - 15) * (1 + fraction / 1024) : 6103515625e-14 * (fraction / 1024));\n    }\n    function parseUint16(dataView, offset2) {\n      var Uint16 = dataView.getUint16(offset2.value, true);\n      offset2.value += INT16_SIZE;\n      return Uint16;\n    }\n    function parseFloat16(buffer2, offset2) {\n      return decodeFloat16(parseUint16(buffer2, offset2));\n    }\n    function parseChlist(dataView, buffer2, offset2, size) {\n      var startOffset = offset2.value;\n      var channels = [];\n      while (offset2.value < startOffset + size - 1) {\n        var name = parseNullTerminatedString(buffer2, offset2);\n        var pixelType = parseInt32(dataView, offset2);\n        var pLinear = parseUint8(dataView, offset2);\n        offset2.value += 3;\n        var xSampling = parseInt32(dataView, offset2);\n        var ySampling = parseInt32(dataView, offset2);\n        channels.push({\n          name,\n          pixelType,\n          pLinear,\n          xSampling,\n          ySampling\n        });\n      }\n      offset2.value += 1;\n      return channels;\n    }\n    function parseChromaticities(dataView, offset2) {\n      var redX = parseFloat32(dataView, offset2);\n      var redY = parseFloat32(dataView, offset2);\n      var greenX = parseFloat32(dataView, offset2);\n      var greenY = parseFloat32(dataView, offset2);\n      var blueX = parseFloat32(dataView, offset2);\n      var blueY = parseFloat32(dataView, offset2);\n      var whiteX = parseFloat32(dataView, offset2);\n      var whiteY = parseFloat32(dataView, offset2);\n      return {\n        redX,\n        redY,\n        greenX,\n        greenY,\n        blueX,\n        blueY,\n        whiteX,\n        whiteY\n      };\n    }\n    function parseCompression(dataView, offset2) {\n      var compressionCodes = [\n        \"NO_COMPRESSION\",\n        \"RLE_COMPRESSION\",\n        \"ZIPS_COMPRESSION\",\n        \"ZIP_COMPRESSION\",\n        \"PIZ_COMPRESSION\",\n        \"PXR24_COMPRESSION\",\n        \"B44_COMPRESSION\",\n        \"B44A_COMPRESSION\",\n        \"DWAA_COMPRESSION\",\n        \"DWAB_COMPRESSION\"\n      ];\n      var compression = parseUint8(dataView, offset2);\n      return compressionCodes[compression];\n    }\n    function parseBox2i(dataView, offset2) {\n      var xMin = parseUint32(dataView, offset2);\n      var yMin = parseUint32(dataView, offset2);\n      var xMax = parseUint32(dataView, offset2);\n      var yMax = parseUint32(dataView, offset2);\n      return { xMin, yMin, xMax, yMax };\n    }\n    function parseLineOrder(dataView, offset2) {\n      var lineOrders = [\"INCREASING_Y\"];\n      var lineOrder = parseUint8(dataView, offset2);\n      return lineOrders[lineOrder];\n    }\n    function parseV2f(dataView, offset2) {\n      var x = parseFloat32(dataView, offset2);\n      var y = parseFloat32(dataView, offset2);\n      return [x, y];\n    }\n    function parseV3f(dataView, offset2) {\n      var x = parseFloat32(dataView, offset2);\n      var y = parseFloat32(dataView, offset2);\n      var z = parseFloat32(dataView, offset2);\n      return [x, y, z];\n    }\n    function parseValue(dataView, buffer2, offset2, type, size) {\n      if (type === \"string\" || type === \"stringvector\" || type === \"iccProfile\") {\n        return parseFixedLengthString(buffer2, offset2, size);\n      } else if (type === \"chlist\") {\n        return parseChlist(dataView, buffer2, offset2, size);\n      } else if (type === \"chromaticities\") {\n        return parseChromaticities(dataView, offset2);\n      } else if (type === \"compression\") {\n        return parseCompression(dataView, offset2);\n      } else if (type === \"box2i\") {\n        return parseBox2i(dataView, offset2);\n      } else if (type === \"lineOrder\") {\n        return parseLineOrder(dataView, offset2);\n      } else if (type === \"float\") {\n        return parseFloat32(dataView, offset2);\n      } else if (type === \"v2f\") {\n        return parseV2f(dataView, offset2);\n      } else if (type === \"v3f\") {\n        return parseV3f(dataView, offset2);\n      } else if (type === \"int\") {\n        return parseInt32(dataView, offset2);\n      } else if (type === \"rational\") {\n        return parseRational(dataView, offset2);\n      } else if (type === \"timecode\") {\n        return parseTimecode(dataView, offset2);\n      } else if (type === \"preview\") {\n        offset2.value += size;\n        return \"skipped\";\n      } else {\n        offset2.value += size;\n        return void 0;\n      }\n    }\n    function parseHeader(dataView, buffer2, offset2) {\n      const EXRHeader2 = {};\n      if (dataView.getUint32(0, true) != 20000630) {\n        throw \"THREE.EXRLoader: provided file doesn't appear to be in OpenEXR format.\";\n      }\n      EXRHeader2.version = dataView.getUint8(4);\n      const spec = dataView.getUint8(5);\n      EXRHeader2.spec = {\n        singleTile: !!(spec & 2),\n        longName: !!(spec & 4),\n        deepFormat: !!(spec & 8),\n        multiPart: !!(spec & 16)\n      };\n      offset2.value = 8;\n      var keepReading = true;\n      while (keepReading) {\n        var attributeName = parseNullTerminatedString(buffer2, offset2);\n        if (attributeName == 0) {\n          keepReading = false;\n        } else {\n          var attributeType = parseNullTerminatedString(buffer2, offset2);\n          var attributeSize = parseUint32(dataView, offset2);\n          var attributeValue = parseValue(dataView, buffer2, offset2, attributeType, attributeSize);\n          if (attributeValue === void 0) {\n            console.warn(`EXRLoader.parse: skipped unknown header attribute type '${attributeType}'.`);\n          } else {\n            EXRHeader2[attributeName] = attributeValue;\n          }\n        }\n      }\n      if ((spec & ~4) != 0) {\n        console.error(\"EXRHeader:\", EXRHeader2);\n        throw \"THREE.EXRLoader: provided file is currently unsupported.\";\n      }\n      return EXRHeader2;\n    }\n    function setupDecoder(EXRHeader2, dataView, uInt8Array2, offset2, outputType) {\n      const EXRDecoder2 = {\n        size: 0,\n        viewer: dataView,\n        array: uInt8Array2,\n        offset: offset2,\n        width: EXRHeader2.dataWindow.xMax - EXRHeader2.dataWindow.xMin + 1,\n        height: EXRHeader2.dataWindow.yMax - EXRHeader2.dataWindow.yMin + 1,\n        channels: EXRHeader2.channels.length,\n        bytesPerLine: null,\n        lines: null,\n        inputSize: null,\n        type: EXRHeader2.channels[0].pixelType,\n        uncompress: null,\n        getter: null,\n        format: null,\n        [hasColorSpace ? \"colorSpace\" : \"encoding\"]: null\n      };\n      switch (EXRHeader2.compression) {\n        case \"NO_COMPRESSION\":\n          EXRDecoder2.lines = 1;\n          EXRDecoder2.uncompress = uncompressRAW;\n          break;\n        case \"RLE_COMPRESSION\":\n          EXRDecoder2.lines = 1;\n          EXRDecoder2.uncompress = uncompressRLE;\n          break;\n        case \"ZIPS_COMPRESSION\":\n          EXRDecoder2.lines = 1;\n          EXRDecoder2.uncompress = uncompressZIP;\n          break;\n        case \"ZIP_COMPRESSION\":\n          EXRDecoder2.lines = 16;\n          EXRDecoder2.uncompress = uncompressZIP;\n          break;\n        case \"PIZ_COMPRESSION\":\n          EXRDecoder2.lines = 32;\n          EXRDecoder2.uncompress = uncompressPIZ;\n          break;\n        case \"PXR24_COMPRESSION\":\n          EXRDecoder2.lines = 16;\n          EXRDecoder2.uncompress = uncompressPXR;\n          break;\n        case \"DWAA_COMPRESSION\":\n          EXRDecoder2.lines = 32;\n          EXRDecoder2.uncompress = uncompressDWA;\n          break;\n        case \"DWAB_COMPRESSION\":\n          EXRDecoder2.lines = 256;\n          EXRDecoder2.uncompress = uncompressDWA;\n          break;\n        default:\n          throw \"EXRLoader.parse: \" + EXRHeader2.compression + \" is unsupported\";\n      }\n      EXRDecoder2.scanlineBlockSize = EXRDecoder2.lines;\n      if (EXRDecoder2.type == 1) {\n        switch (outputType) {\n          case three__WEBPACK_IMPORTED_MODULE_1__.FloatType:\n            EXRDecoder2.getter = parseFloat16;\n            EXRDecoder2.inputSize = INT16_SIZE;\n            break;\n          case three__WEBPACK_IMPORTED_MODULE_1__.HalfFloatType:\n            EXRDecoder2.getter = parseUint16;\n            EXRDecoder2.inputSize = INT16_SIZE;\n            break;\n        }\n      } else if (EXRDecoder2.type == 2) {\n        switch (outputType) {\n          case three__WEBPACK_IMPORTED_MODULE_1__.FloatType:\n            EXRDecoder2.getter = parseFloat32;\n            EXRDecoder2.inputSize = FLOAT32_SIZE;\n            break;\n          case three__WEBPACK_IMPORTED_MODULE_1__.HalfFloatType:\n            EXRDecoder2.getter = decodeFloat32;\n            EXRDecoder2.inputSize = FLOAT32_SIZE;\n        }\n      } else {\n        throw \"EXRLoader.parse: unsupported pixelType \" + EXRDecoder2.type + \" for \" + EXRHeader2.compression + \".\";\n      }\n      EXRDecoder2.blockCount = (EXRHeader2.dataWindow.yMax + 1) / EXRDecoder2.scanlineBlockSize;\n      for (var i = 0; i < EXRDecoder2.blockCount; i++)\n        parseInt64(dataView, offset2);\n      EXRDecoder2.outputChannels = EXRDecoder2.channels == 3 ? 4 : EXRDecoder2.channels;\n      const size = EXRDecoder2.width * EXRDecoder2.height * EXRDecoder2.outputChannels;\n      switch (outputType) {\n        case three__WEBPACK_IMPORTED_MODULE_1__.FloatType:\n          EXRDecoder2.byteArray = new Float32Array(size);\n          if (EXRDecoder2.channels < EXRDecoder2.outputChannels)\n            EXRDecoder2.byteArray.fill(1, 0, size);\n          break;\n        case three__WEBPACK_IMPORTED_MODULE_1__.HalfFloatType:\n          EXRDecoder2.byteArray = new Uint16Array(size);\n          if (EXRDecoder2.channels < EXRDecoder2.outputChannels)\n            EXRDecoder2.byteArray.fill(15360, 0, size);\n          break;\n        default:\n          console.error(\"THREE.EXRLoader: unsupported type: \", outputType);\n          break;\n      }\n      EXRDecoder2.bytesPerLine = EXRDecoder2.width * EXRDecoder2.inputSize * EXRDecoder2.channels;\n      if (EXRDecoder2.outputChannels == 4)\n        EXRDecoder2.format = three__WEBPACK_IMPORTED_MODULE_1__.RGBAFormat;\n      else\n        EXRDecoder2.format = three__WEBPACK_IMPORTED_MODULE_1__.RedFormat;\n      if (hasColorSpace)\n        EXRDecoder2.colorSpace = \"srgb-linear\";\n      else\n        EXRDecoder2.encoding = 3e3;\n      return EXRDecoder2;\n    }\n    const bufferDataView = new DataView(buffer);\n    const uInt8Array = new Uint8Array(buffer);\n    const offset = { value: 0 };\n    const EXRHeader = parseHeader(bufferDataView, buffer, offset);\n    const EXRDecoder = setupDecoder(EXRHeader, bufferDataView, uInt8Array, offset, this.type);\n    const tmpOffset = { value: 0 };\n    const channelOffsets = { R: 0, G: 1, B: 2, A: 3, Y: 0 };\n    for (let scanlineBlockIdx = 0; scanlineBlockIdx < EXRDecoder.height / EXRDecoder.scanlineBlockSize; scanlineBlockIdx++) {\n      const line = parseUint32(bufferDataView, offset);\n      EXRDecoder.size = parseUint32(bufferDataView, offset);\n      EXRDecoder.lines = line + EXRDecoder.scanlineBlockSize > EXRDecoder.height ? EXRDecoder.height - line : EXRDecoder.scanlineBlockSize;\n      const isCompressed = EXRDecoder.size < EXRDecoder.lines * EXRDecoder.bytesPerLine;\n      const viewer = isCompressed ? EXRDecoder.uncompress(EXRDecoder) : uncompressRAW(EXRDecoder);\n      offset.value += EXRDecoder.size;\n      for (let line_y = 0; line_y < EXRDecoder.scanlineBlockSize; line_y++) {\n        const true_y = line_y + scanlineBlockIdx * EXRDecoder.scanlineBlockSize;\n        if (true_y >= EXRDecoder.height)\n          break;\n        for (let channelID = 0; channelID < EXRDecoder.channels; channelID++) {\n          const cOff = channelOffsets[EXRHeader.channels[channelID].name];\n          for (let x = 0; x < EXRDecoder.width; x++) {\n            tmpOffset.value = (line_y * (EXRDecoder.channels * EXRDecoder.width) + channelID * EXRDecoder.width + x) * EXRDecoder.inputSize;\n            const outIndex = (EXRDecoder.height - 1 - true_y) * (EXRDecoder.width * EXRDecoder.outputChannels) + x * EXRDecoder.outputChannels + cOff;\n            EXRDecoder.byteArray[outIndex] = EXRDecoder.getter(viewer, tmpOffset);\n          }\n        }\n      }\n    }\n    return {\n      header: EXRHeader,\n      width: EXRDecoder.width,\n      height: EXRDecoder.height,\n      data: EXRDecoder.byteArray,\n      format: EXRDecoder.format,\n      [hasColorSpace ? \"colorSpace\" : \"encoding\"]: EXRDecoder[hasColorSpace ? \"colorSpace\" : \"encoding\"],\n      type: this.type\n    };\n  }\n  setDataType(value) {\n    this.type = value;\n    return this;\n  }\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      if (hasColorSpace)\n        texture.colorSpace = texData.colorSpace;\n      else\n        texture.encoding = texData.encoding;\n      texture.minFilter = three__WEBPACK_IMPORTED_MODULE_1__.LinearFilter;\n      texture.magFilter = three__WEBPACK_IMPORTED_MODULE_1__.LinearFilter;\n      texture.generateMipmaps = false;\n      texture.flipY = false;\n      if (onLoad)\n        onLoad(texture, texData);\n    }\n    return super.load(url, onLoadCallback, onProgress, onError);\n  }\n}\n\n//# sourceMappingURL=EXRLoader.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/loaders/EXRLoader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/loaders/RGBELoader.js":
/*!*********************************************************!*\
  !*** ./node_modules/three-stdlib/loaders/RGBELoader.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RGBELoader: () => (/* binding */ RGBELoader)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n\nclass RGBELoader extends three__WEBPACK_IMPORTED_MODULE_0__.DataTextureLoader {\n  constructor(manager) {\n    super(manager);\n    this.type = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n  }\n  // adapted from http://www.graphics.cornell.edu/~bjw/rgbe.html\n  parse(buffer) {\n    const rgbe_read_error = 1, rgbe_write_error = 2, rgbe_format_error = 3, rgbe_memory_error = 4, rgbe_error = function(rgbe_error_code, msg) {\n      switch (rgbe_error_code) {\n        case rgbe_read_error:\n          throw new Error(\"THREE.RGBELoader: Read Error: \" + (msg || \"\"));\n        case rgbe_write_error:\n          throw new Error(\"THREE.RGBELoader: Write Error: \" + (msg || \"\"));\n        case rgbe_format_error:\n          throw new Error(\"THREE.RGBELoader: Bad File Format: \" + (msg || \"\"));\n        default:\n        case rgbe_memory_error:\n          throw new Error(\"THREE.RGBELoader: Memory Error: \" + (msg || \"\"));\n      }\n    }, RGBE_VALID_PROGRAMTYPE = 1, RGBE_VALID_FORMAT = 2, RGBE_VALID_DIMENSIONS = 4, NEWLINE = \"\\n\", fgets = function(buffer2, lineLimit, consume) {\n      const chunkSize = 128;\n      lineLimit = !lineLimit ? 1024 : lineLimit;\n      let p = buffer2.pos, i = -1, len = 0, s = \"\", chunk = String.fromCharCode.apply(null, new Uint16Array(buffer2.subarray(p, p + chunkSize)));\n      while (0 > (i = chunk.indexOf(NEWLINE)) && len < lineLimit && p < buffer2.byteLength) {\n        s += chunk;\n        len += chunk.length;\n        p += chunkSize;\n        chunk += String.fromCharCode.apply(null, new Uint16Array(buffer2.subarray(p, p + chunkSize)));\n      }\n      if (-1 < i) {\n        if (false !== consume)\n          buffer2.pos += len + i + 1;\n        return s + chunk.slice(0, i);\n      }\n      return false;\n    }, RGBE_ReadHeader = function(buffer2) {\n      const magic_token_re = /^#\\?(\\S+)/, gamma_re = /^\\s*GAMMA\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/, exposure_re = /^\\s*EXPOSURE\\s*=\\s*(\\d+(\\.\\d+)?)\\s*$/, format_re = /^\\s*FORMAT=(\\S+)\\s*$/, dimensions_re = /^\\s*\\-Y\\s+(\\d+)\\s+\\+X\\s+(\\d+)\\s*$/, header = {\n        valid: 0,\n        string: \"\",\n        comments: \"\",\n        programtype: \"RGBE\",\n        format: \"\",\n        gamma: 1,\n        exposure: 1,\n        width: 0,\n        height: 0\n      };\n      let line, match;\n      if (buffer2.pos >= buffer2.byteLength || !(line = fgets(buffer2))) {\n        rgbe_error(rgbe_read_error, \"no header found\");\n      }\n      if (!(match = line.match(magic_token_re))) {\n        rgbe_error(rgbe_format_error, \"bad initial token\");\n      }\n      header.valid |= RGBE_VALID_PROGRAMTYPE;\n      header.programtype = match[1];\n      header.string += line + \"\\n\";\n      while (true) {\n        line = fgets(buffer2);\n        if (false === line)\n          break;\n        header.string += line + \"\\n\";\n        if (\"#\" === line.charAt(0)) {\n          header.comments += line + \"\\n\";\n          continue;\n        }\n        if (match = line.match(gamma_re)) {\n          header.gamma = parseFloat(match[1]);\n        }\n        if (match = line.match(exposure_re)) {\n          header.exposure = parseFloat(match[1]);\n        }\n        if (match = line.match(format_re)) {\n          header.valid |= RGBE_VALID_FORMAT;\n          header.format = match[1];\n        }\n        if (match = line.match(dimensions_re)) {\n          header.valid |= RGBE_VALID_DIMENSIONS;\n          header.height = parseInt(match[1], 10);\n          header.width = parseInt(match[2], 10);\n        }\n        if (header.valid & RGBE_VALID_FORMAT && header.valid & RGBE_VALID_DIMENSIONS)\n          break;\n      }\n      if (!(header.valid & RGBE_VALID_FORMAT)) {\n        rgbe_error(rgbe_format_error, \"missing format specifier\");\n      }\n      if (!(header.valid & RGBE_VALID_DIMENSIONS)) {\n        rgbe_error(rgbe_format_error, \"missing image size specifier\");\n      }\n      return header;\n    }, RGBE_ReadPixels_RLE = function(buffer2, w2, h2) {\n      const scanline_width = w2;\n      if (\n        // run length encoding is not allowed so read flat\n        scanline_width < 8 || scanline_width > 32767 || // this file is not run length encoded\n        2 !== buffer2[0] || 2 !== buffer2[1] || buffer2[2] & 128\n      ) {\n        return new Uint8Array(buffer2);\n      }\n      if (scanline_width !== (buffer2[2] << 8 | buffer2[3])) {\n        rgbe_error(rgbe_format_error, \"wrong scanline width\");\n      }\n      const data_rgba = new Uint8Array(4 * w2 * h2);\n      if (!data_rgba.length) {\n        rgbe_error(rgbe_memory_error, \"unable to allocate buffer space\");\n      }\n      let offset = 0, pos = 0;\n      const ptr_end = 4 * scanline_width;\n      const rgbeStart = new Uint8Array(4);\n      const scanline_buffer = new Uint8Array(ptr_end);\n      let num_scanlines = h2;\n      while (num_scanlines > 0 && pos < buffer2.byteLength) {\n        if (pos + 4 > buffer2.byteLength) {\n          rgbe_error(rgbe_read_error);\n        }\n        rgbeStart[0] = buffer2[pos++];\n        rgbeStart[1] = buffer2[pos++];\n        rgbeStart[2] = buffer2[pos++];\n        rgbeStart[3] = buffer2[pos++];\n        if (2 != rgbeStart[0] || 2 != rgbeStart[1] || (rgbeStart[2] << 8 | rgbeStart[3]) != scanline_width) {\n          rgbe_error(rgbe_format_error, \"bad rgbe scanline format\");\n        }\n        let ptr = 0, count;\n        while (ptr < ptr_end && pos < buffer2.byteLength) {\n          count = buffer2[pos++];\n          const isEncodedRun = count > 128;\n          if (isEncodedRun)\n            count -= 128;\n          if (0 === count || ptr + count > ptr_end) {\n            rgbe_error(rgbe_format_error, \"bad scanline data\");\n          }\n          if (isEncodedRun) {\n            const byteValue = buffer2[pos++];\n            for (let i = 0; i < count; i++) {\n              scanline_buffer[ptr++] = byteValue;\n            }\n          } else {\n            scanline_buffer.set(buffer2.subarray(pos, pos + count), ptr);\n            ptr += count;\n            pos += count;\n          }\n        }\n        const l = scanline_width;\n        for (let i = 0; i < l; i++) {\n          let off = 0;\n          data_rgba[offset] = scanline_buffer[i + off];\n          off += scanline_width;\n          data_rgba[offset + 1] = scanline_buffer[i + off];\n          off += scanline_width;\n          data_rgba[offset + 2] = scanline_buffer[i + off];\n          off += scanline_width;\n          data_rgba[offset + 3] = scanline_buffer[i + off];\n          offset += 4;\n        }\n        num_scanlines--;\n      }\n      return data_rgba;\n    };\n    const RGBEByteToRGBFloat = function(sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3];\n      const scale = Math.pow(2, e - 128) / 255;\n      destArray[destOffset + 0] = sourceArray[sourceOffset + 0] * scale;\n      destArray[destOffset + 1] = sourceArray[sourceOffset + 1] * scale;\n      destArray[destOffset + 2] = sourceArray[sourceOffset + 2] * scale;\n      destArray[destOffset + 3] = 1;\n    };\n    const RGBEByteToRGBHalf = function(sourceArray, sourceOffset, destArray, destOffset) {\n      const e = sourceArray[sourceOffset + 3];\n      const scale = Math.pow(2, e - 128) / 255;\n      destArray[destOffset + 0] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 0] * scale, 65504));\n      destArray[destOffset + 1] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 1] * scale, 65504));\n      destArray[destOffset + 2] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat(Math.min(sourceArray[sourceOffset + 2] * scale, 65504));\n      destArray[destOffset + 3] = three__WEBPACK_IMPORTED_MODULE_0__.DataUtils.toHalfFloat(1);\n    };\n    const byteArray = new Uint8Array(buffer);\n    byteArray.pos = 0;\n    const rgbe_header_info = RGBE_ReadHeader(byteArray);\n    const w = rgbe_header_info.width, h = rgbe_header_info.height, image_rgba_data = RGBE_ReadPixels_RLE(byteArray.subarray(byteArray.pos), w, h);\n    let data, type;\n    let numElements;\n    switch (this.type) {\n      case three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n        numElements = image_rgba_data.length / 4;\n        const floatArray = new Float32Array(numElements * 4);\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBFloat(image_rgba_data, j * 4, floatArray, j * 4);\n        }\n        data = floatArray;\n        type = three__WEBPACK_IMPORTED_MODULE_0__.FloatType;\n        break;\n      case three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n        numElements = image_rgba_data.length / 4;\n        const halfArray = new Uint16Array(numElements * 4);\n        for (let j = 0; j < numElements; j++) {\n          RGBEByteToRGBHalf(image_rgba_data, j * 4, halfArray, j * 4);\n        }\n        data = halfArray;\n        type = three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType;\n        break;\n      default:\n        throw new Error(\"THREE.RGBELoader: Unsupported type: \" + this.type);\n    }\n    return {\n      width: w,\n      height: h,\n      data,\n      header: rgbe_header_info.string,\n      gamma: rgbe_header_info.gamma,\n      exposure: rgbe_header_info.exposure,\n      type\n    };\n  }\n  setDataType(value) {\n    this.type = value;\n    return this;\n  }\n  load(url, onLoad, onProgress, onError) {\n    function onLoadCallback(texture, texData) {\n      switch (texture.type) {\n        case three__WEBPACK_IMPORTED_MODULE_0__.FloatType:\n        case three__WEBPACK_IMPORTED_MODULE_0__.HalfFloatType:\n          if (\"colorSpace\" in texture)\n            texture.colorSpace = \"srgb-linear\";\n          else\n            texture.encoding = 3e3;\n          texture.minFilter = three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter;\n          texture.magFilter = three__WEBPACK_IMPORTED_MODULE_0__.LinearFilter;\n          texture.generateMipmaps = false;\n          texture.flipY = true;\n          break;\n      }\n      if (onLoad)\n        onLoad(texture, texData);\n    }\n    return super.load(url, onLoadCallback, onProgress, onError);\n  }\n}\n\n//# sourceMappingURL=RGBELoader.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/loaders/RGBELoader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/node_modules/fflate/esm/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/three-stdlib/node_modules/fflate/esm/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsyncCompress: () => (/* binding */ AsyncGzip),\n/* harmony export */   AsyncDecompress: () => (/* binding */ AsyncDecompress),\n/* harmony export */   AsyncDeflate: () => (/* binding */ AsyncDeflate),\n/* harmony export */   AsyncGunzip: () => (/* binding */ AsyncGunzip),\n/* harmony export */   AsyncGzip: () => (/* binding */ AsyncGzip),\n/* harmony export */   AsyncInflate: () => (/* binding */ AsyncInflate),\n/* harmony export */   AsyncUnzipInflate: () => (/* binding */ AsyncUnzipInflate),\n/* harmony export */   AsyncUnzlib: () => (/* binding */ AsyncUnzlib),\n/* harmony export */   AsyncZipDeflate: () => (/* binding */ AsyncZipDeflate),\n/* harmony export */   AsyncZlib: () => (/* binding */ AsyncZlib),\n/* harmony export */   Compress: () => (/* binding */ Gzip),\n/* harmony export */   DecodeUTF8: () => (/* binding */ DecodeUTF8),\n/* harmony export */   Decompress: () => (/* binding */ Decompress),\n/* harmony export */   Deflate: () => (/* binding */ Deflate),\n/* harmony export */   EncodeUTF8: () => (/* binding */ EncodeUTF8),\n/* harmony export */   Gunzip: () => (/* binding */ Gunzip),\n/* harmony export */   Gzip: () => (/* binding */ Gzip),\n/* harmony export */   Inflate: () => (/* binding */ Inflate),\n/* harmony export */   Unzip: () => (/* binding */ Unzip),\n/* harmony export */   UnzipInflate: () => (/* binding */ UnzipInflate),\n/* harmony export */   UnzipPassThrough: () => (/* binding */ UnzipPassThrough),\n/* harmony export */   Unzlib: () => (/* binding */ Unzlib),\n/* harmony export */   Zip: () => (/* binding */ Zip),\n/* harmony export */   ZipDeflate: () => (/* binding */ ZipDeflate),\n/* harmony export */   ZipPassThrough: () => (/* binding */ ZipPassThrough),\n/* harmony export */   Zlib: () => (/* binding */ Zlib),\n/* harmony export */   compress: () => (/* binding */ gzip),\n/* harmony export */   compressSync: () => (/* binding */ gzipSync),\n/* harmony export */   decompress: () => (/* binding */ decompress),\n/* harmony export */   decompressSync: () => (/* binding */ decompressSync),\n/* harmony export */   deflate: () => (/* binding */ deflate),\n/* harmony export */   deflateSync: () => (/* binding */ deflateSync),\n/* harmony export */   gunzip: () => (/* binding */ gunzip),\n/* harmony export */   gunzipSync: () => (/* binding */ gunzipSync),\n/* harmony export */   gzip: () => (/* binding */ gzip),\n/* harmony export */   gzipSync: () => (/* binding */ gzipSync),\n/* harmony export */   inflate: () => (/* binding */ inflate),\n/* harmony export */   inflateSync: () => (/* binding */ inflateSync),\n/* harmony export */   strFromU8: () => (/* binding */ strFromU8),\n/* harmony export */   strToU8: () => (/* binding */ strToU8),\n/* harmony export */   unzip: () => (/* binding */ unzip),\n/* harmony export */   unzipSync: () => (/* binding */ unzipSync),\n/* harmony export */   unzlib: () => (/* binding */ unzlib),\n/* harmony export */   unzlibSync: () => (/* binding */ unzlibSync),\n/* harmony export */   zip: () => (/* binding */ zip),\n/* harmony export */   zipSync: () => (/* binding */ zipSync),\n/* harmony export */   zlib: () => (/* binding */ zlib),\n/* harmony export */   zlibSync: () => (/* binding */ zlibSync)\n/* harmony export */ });\n/* harmony import */ var module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! module */ \"module\");\n\nvar require = (0,module__WEBPACK_IMPORTED_MODULE_0__.createRequire)('/');\n// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\n// Mediocre shim\nvar Worker;\nvar workerAdd = \";var __w=require('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global\";\ntry {\n    Worker = require('worker_threads').Worker;\n}\ncatch (e) {\n}\nvar wk = Worker ? function (c, _, msg, transfer, cb) {\n    var done = false;\n    var w = new Worker(c + workerAdd, { eval: true })\n        .on('error', function (e) { return cb(e, null); })\n        .on('message', function (m) { return cb(null, m); })\n        .on('exit', function (c) {\n        if (c && !done)\n            cb(new Error('exited with code ' + c), null);\n    });\n    w.postMessage(msg, transfer);\n    w.terminate = function () {\n        done = true;\n        return Worker.prototype.terminate.call(w);\n    };\n    return w;\n} : function (_, __, ___, ____, cb) {\n    setImmediate(function () { return cb(new Error('async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)'), null); });\n    var NOP = function () { };\n    return {\n        terminate: NOP,\n        postMessage: NOP\n    };\n};\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) | 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    if (!sl || (st && !st.l && sl < 5))\n        return buf || new u8(0);\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [et, 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            // deopt when i > s - 3 - at end, deopt acceptable\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst && pos & 7)\n            pos = wfblk(w, pos + 1, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new Int32Array(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && -306674912) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return ((d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) | (d[l - 1] << 24)) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\n\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\n\nfunction deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nfunction deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\n\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\n\nfunction inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nfunction inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\n\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\n\nfunction gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nfunction gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = this.p.length > 3 ? gzs(this.p) : 4;\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\n\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\n\nfunction gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nfunction gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\n\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\n\nfunction zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nfunction zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\n\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\n\nfunction unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nfunction unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\n\n// Default algorithm for compression (used because having a known output size allows faster decompression)\n\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\n\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\n\nfunction decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nfunction decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return [r, slc(d, i - 1)];\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    throw 'invalid utf-8 data';\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            throw 'stream finished';\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), ch = _a[0], np = _a[1];\n        if (final) {\n            if (np.length)\n                throw 'invalid utf-8 data';\n            this.p = null;\n        }\n        else\n            this.p = np;\n        this.ondata(ch, final);\n    };\n    return DecodeUTF8;\n}());\n\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        if (this.d)\n            throw 'stream finished';\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\n\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nfunction strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nfunction strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td)\n        return td.decode(dat);\n    else {\n        var _a = dutf8(dat), out = _a[0], ext = _a[1];\n        if (ext.length)\n            throw 'invalid utf-8 data';\n        return out;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                throw 'extra field too long';\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c == null && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1)), b += 4;\n    if (c != null) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback - add to ZIP archive before pushing';\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\n\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\n\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this_1.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\n\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this_1 = this;\n        if (this.d & 2)\n            throw 'stream finished';\n        var f = strToU8(file.filename), fl = f.length;\n        var com = file.comment, o = com && strToU8(com);\n        var u = fl != file.filename.length || (o && (com.length != o.length));\n        var hl = fl + exfl(file.extra) + 30;\n        if (fl > 65535)\n            throw 'filename too long';\n        var header = new u8(hl);\n        wzh(header, 0, file, f, u);\n        var chks = [header];\n        var pAll = function () {\n            for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n                var chk = chks_1[_i];\n                _this_1.ondata(null, chk, false);\n            }\n            chks = [];\n        };\n        var tr = this.d;\n        this.d = 0;\n        var ind = this.u.length;\n        var uf = mrg(file, {\n            f: f,\n            u: u,\n            o: o,\n            t: function () {\n                if (file.terminate)\n                    file.terminate();\n            },\n            r: function () {\n                pAll();\n                if (tr) {\n                    var nxt = _this_1.u[ind + 1];\n                    if (nxt)\n                        nxt.r();\n                    else\n                        _this_1.d = 1;\n                }\n                tr = 1;\n            }\n        });\n        var cl = 0;\n        file.ondata = function (err, dat, final) {\n            if (err) {\n                _this_1.ondata(err, dat, final);\n                _this_1.terminate();\n            }\n            else {\n                cl += dat.length;\n                chks.push(dat);\n                if (final) {\n                    var dd = new u8(16);\n                    wbytes(dd, 0, 0x8074B50);\n                    wbytes(dd, 4, file.crc);\n                    wbytes(dd, 8, cl);\n                    wbytes(dd, 12, file.size);\n                    chks.push(dd);\n                    uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n                    if (tr)\n                        uf.r();\n                    tr = 1;\n                }\n                else if (tr)\n                    pAll();\n            }\n        };\n        this.u.push(uf);\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this_1 = this;\n        if (this.d & 2) {\n            if (this.d & 1)\n                throw 'stream finishing';\n            throw 'stream finished';\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this_1.d & 1))\n                        return;\n                    _this_1.u.splice(-1, 1);\n                    _this_1.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\n\nfunction zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl('filename too long', null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nfunction zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            throw 'filename too long';\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\n\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this_1 = this;\n        this.i = new Inflate(function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, data, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\n\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this_1 = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this_1.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this_1.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\n\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this_1 = this;\n        if (!this.onfile)\n            throw 'no callback';\n        if (!this.p)\n            throw 'stream finished';\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_2 = [];\n                        this_1.k.unshift(chks_2);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    throw 'no callback';\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this_1.o[cmp_1];\n                                    if (!ctr)\n                                        throw 'unknown compression type ' + cmp_1;\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                                        var dat = chks_3[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this_1.k[0] == chks_2 && _this_1.c)\n                                        _this_1.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                throw 'invalid zip file';\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\n\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nfunction unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50) {\n            cb('invalid zip file', null);\n            return;\n        }\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_3 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_3(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nfunction unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/node_modules/fflate/esm/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/three-stdlib/objects/GroundProjectedEnv.js":
/*!*****************************************************************!*\
  !*** ./node_modules/three-stdlib/objects/GroundProjectedEnv.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GroundProjectedEnv: () => (/* binding */ GroundProjectedEnv)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var _polyfill_constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../_polyfill/constants.js */ \"(ssr)/./node_modules/three-stdlib/_polyfill/constants.js\");\n\n\nconst isCubeTexture = (def) => def && def.isCubeTexture;\nclass GroundProjectedEnv extends three__WEBPACK_IMPORTED_MODULE_0__.Mesh {\n  constructor(texture, options) {\n    var _a, _b;\n    const isCubeMap = isCubeTexture(texture);\n    const w = (_b = isCubeMap ? (_a = texture.image[0]) == null ? void 0 : _a.width : texture.image.width) != null ? _b : 1024;\n    const cubeSize = w / 4;\n    const _lodMax = Math.floor(Math.log2(cubeSize));\n    const _cubeSize = Math.pow(2, _lodMax);\n    const width = 3 * Math.max(_cubeSize, 16 * 7);\n    const height = 4 * _cubeSize;\n    const defines = [\n      isCubeMap ? \"#define ENVMAP_TYPE_CUBE\" : \"\",\n      `#define CUBEUV_TEXEL_WIDTH ${1 / width}`,\n      `#define CUBEUV_TEXEL_HEIGHT ${1 / height}`,\n      `#define CUBEUV_MAX_MIP ${_lodMax}.0`\n    ];\n    const vertexShader = (\n      /* glsl */\n      `\n        varying vec3 vWorldPosition;\n        void main() \n        {\n            vec4 worldPosition = ( modelMatrix * vec4( position, 1.0 ) );\n            vWorldPosition = worldPosition.xyz;\n            \n            gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n        }\n        `\n    );\n    const fragmentShader = defines.join(\"\\n\") + /* glsl */\n    `\n        #define ENVMAP_TYPE_CUBE_UV\n        varying vec3 vWorldPosition;\n        uniform float radius;\n        uniform float height;\n        uniform float angle;\n        #ifdef ENVMAP_TYPE_CUBE\n            uniform samplerCube map;\n        #else\n            uniform sampler2D map;\n        #endif\n        // From: https://www.shadertoy.com/view/4tsBD7\n        float diskIntersectWithBackFaceCulling( vec3 ro, vec3 rd, vec3 c, vec3 n, float r ) \n        {\n            float d = dot ( rd, n );\n            \n            if( d > 0.0 ) { return 1e6; }\n            \n            vec3  o = ro - c;\n            float t = - dot( n, o ) / d;\n            vec3  q = o + rd * t;\n            \n            return ( dot( q, q ) < r * r ) ? t : 1e6;\n        }\n        // From: https://www.iquilezles.org/www/articles/intersectors/intersectors.htm\n        float sphereIntersect( vec3 ro, vec3 rd, vec3 ce, float ra ) \n        {\n            vec3 oc = ro - ce;\n            float b = dot( oc, rd );\n            float c = dot( oc, oc ) - ra * ra;\n            float h = b * b - c;\n            \n            if( h < 0.0 ) { return -1.0; }\n            \n            h = sqrt( h );\n            \n            return - b + h;\n        }\n        vec3 project() \n        {\n            vec3 p = normalize( vWorldPosition );\n            vec3 camPos = cameraPosition;\n            camPos.y -= height;\n            float intersection = sphereIntersect( camPos, p, vec3( 0.0 ), radius );\n            if( intersection > 0.0 ) {\n                \n                vec3 h = vec3( 0.0, - height, 0.0 );\n                float intersection2 = diskIntersectWithBackFaceCulling( camPos, p, h, vec3( 0.0, 1.0, 0.0 ), radius );\n                p = ( camPos + min( intersection, intersection2 ) * p ) / radius;\n            } else {\n                p = vec3( 0.0, 1.0, 0.0 );\n            }\n            return p;\n        }\n        #include <common>\n        #include <cube_uv_reflection_fragment>\n        void main() \n        {\n            vec3 projectedWorldPosition = project();\n            \n            #ifdef ENVMAP_TYPE_CUBE\n                vec3 outcolor = textureCube( map, projectedWorldPosition ).rgb;\n            #else\n                vec3 direction = normalize( projectedWorldPosition );\n                vec2 uv = equirectUv( direction );\n                vec3 outcolor = texture2D( map, uv ).rgb;\n            #endif\n            gl_FragColor = vec4( outcolor, 1.0 );\n            #include <tonemapping_fragment>\n            #include <${_polyfill_constants_js__WEBPACK_IMPORTED_MODULE_1__.version >= 154 ? \"colorspace_fragment\" : \"encodings_fragment\"}>\n        }\n        `;\n    const uniforms = {\n      map: { value: texture },\n      height: { value: (options == null ? void 0 : options.height) || 15 },\n      radius: { value: (options == null ? void 0 : options.radius) || 100 }\n    };\n    const geometry = new three__WEBPACK_IMPORTED_MODULE_0__.IcosahedronGeometry(1, 16);\n    const material = new three__WEBPACK_IMPORTED_MODULE_0__.ShaderMaterial({\n      uniforms,\n      fragmentShader,\n      vertexShader,\n      side: three__WEBPACK_IMPORTED_MODULE_0__.DoubleSide\n    });\n    super(geometry, material);\n  }\n  set radius(radius) {\n    this.material.uniforms.radius.value = radius;\n  }\n  get radius() {\n    return this.material.uniforms.radius.value;\n  }\n  set height(height) {\n    this.material.uniforms.height.value = height;\n  }\n  get height() {\n    return this.material.uniforms.height.value;\n  }\n}\n\n//# sourceMappingURL=GroundProjectedEnv.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/three-stdlib/objects/GroundProjectedEnv.js\n");

/***/ })

};
;