{"version": 3, "file": "CopyShader.js", "sources": ["../../src/shaders/CopyShader.ts"], "sourcesContent": ["/**\n * Full-screen textured quad shader\n */\n\nimport type { IUniform, Texture } from 'three'\nimport type { IShader } from './types'\n\nexport type CopyShaderUniforms = {\n  opacity: IUniform<number>\n  tDiffuse: IUniform<Texture | null>\n}\n\nexport interface ICopyShader extends IShader<CopyShaderUniforms> {}\n\nexport const CopyShader: ICopyShader = {\n  uniforms: {\n    tDiffuse: { value: null },\n    opacity: { value: 1.0 },\n  },\n\n  vertexShader: /* glsl */ `\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvUv = uv;\n    \tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n\n    }\n  `,\n\n  fragmentShader: /* glsl */ `\n    uniform float opacity;\n\n    uniform sampler2D tDiffuse;\n\n    varying vec2 vUv;\n\n    void main() {\n\n    \tvec4 texel = texture2D( tDiffuse, vUv );\n    \tgl_FragColor = opacity * texel;\n\n    }\n  `,\n}\n"], "names": [], "mappings": "AAcO,MAAM,aAA0B;AAAA,EACrC,UAAU;AAAA,IACR,UAAU,EAAE,OAAO,KAAK;AAAA,IACxB,SAAS,EAAE,OAAO,EAAI;AAAA,EACxB;AAAA,EAEA;AAAA;AAAA,IAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB;AAAA;AAAA,IAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAc7B;"}