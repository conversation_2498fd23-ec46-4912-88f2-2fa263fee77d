"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-three";
exports.ids = ["vendor-chunks/@react-three"];
exports.modules = {

/***/ "(ssr)/./node_modules/@react-three/drei/core/Environment.js":
/*!************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/Environment.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Environment: () => (/* binding */ Environment),\n/* harmony export */   EnvironmentCube: () => (/* binding */ EnvironmentCube),\n/* harmony export */   EnvironmentMap: () => (/* binding */ EnvironmentMap),\n/* harmony export */   EnvironmentPortal: () => (/* binding */ EnvironmentPortal)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/objects/GroundProjectedEnv.js\");\n/* harmony import */ var _useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useEnvironment.js */ \"(ssr)/./node_modules/@react-three/drei/core/useEnvironment.js\");\n\n\n\n\n\n\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.q)(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.q)(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.scene);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = (0,_useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__.useEnvironment)(rest);\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.scene);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.gl);\n  const defaultScene = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.scene);\n  const camera = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const [virtualScene] = react__WEBPACK_IMPORTED_MODULE_1__.useState(() => new three__WEBPACK_IMPORTED_MODULE_4__.Scene());\n  const fbo = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n    const fbo = new three__WEBPACK_IMPORTED_MODULE_4__.WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = three__WEBPACK_IMPORTED_MODULE_4__.HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.C)(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.o)(/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, children, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = (0,_useEnvironment_js__WEBPACK_IMPORTED_MODULE_3__.useEnvironment)(props);\n  const texture = props.map || textureDefault;\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.e)({\n    GroundProjectedEnvImpl: three_stdlib__WEBPACK_IMPORTED_MODULE_5__.GroundProjectedEnv\n  }), []);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    map: texture\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentPortal, props) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(EnvironmentCube, props);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/Environment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/Float.js":
/*!******************************************************!*\
  !*** ./node_modules/@react-three/drei/core/Float.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Float: () => (/* binding */ Float)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n\n\n\n\nconst Float = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({\n  children,\n  enabled = true,\n  speed = 1,\n  rotationIntensity = 1,\n  floatIntensity = 1,\n  floatingRange = [-0.1, 0.1],\n  autoInvalidate = false,\n  ...props\n}, forwardRef) => {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(forwardRef, () => ref.current, []);\n  const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(Math.random() * 10000);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_1__.C)(state => {\n    var _floatingRange$, _floatingRange$2;\n    if (!enabled || speed === 0) return;\n    if (autoInvalidate) state.invalidate();\n    const t = offset.current + state.clock.elapsedTime;\n    ref.current.rotation.x = Math.cos(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.y = Math.sin(t / 4 * speed) / 8 * rotationIntensity;\n    ref.current.rotation.z = Math.sin(t / 4 * speed) / 20 * rotationIntensity;\n    let yPosition = Math.sin(t / 4 * speed) / 10;\n    yPosition = three__WEBPACK_IMPORTED_MODULE_2__.MathUtils.mapLinear(yPosition, -0.1, 0.1, (_floatingRange$ = floatingRange == null ? void 0 : floatingRange[0]) !== null && _floatingRange$ !== void 0 ? _floatingRange$ : -0.1, (_floatingRange$2 = floatingRange == null ? void 0 : floatingRange[1]) !== null && _floatingRange$2 !== void 0 ? _floatingRange$2 : 0.1);\n    ref.current.position.y = yPosition * floatIntensity;\n    ref.current.updateMatrix();\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", props, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"group\", {\n    ref: ref,\n    matrixAutoUpdate: false\n  }, children));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/Float.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js":
/*!**************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/OrbitControls.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrbitControls: () => (/* binding */ OrbitControls)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/controls/OrbitControls.js\");\n\n\n\n\n\nconst OrbitControls = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.invalidate);\n  const defaultCamera = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.camera);\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.gl);\n  const events = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.events);\n  const setEvents = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.setEvents);\n  const set = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.set);\n  const get = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.get);\n  const performance = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => new three_stdlib__WEBPACK_IMPORTED_MODULE_3__.OrbitControls(explCamera), [explCamera]);\n  (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.C)(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"primitive\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/OrbitControls.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/core/useEnvironment.js":
/*!***************************************************************!*\
  !*** ./node_modules/@react-three/drei/core/useEnvironment.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEnvironment: () => (/* binding */ useEnvironment)\n/* harmony export */ });\n/* harmony import */ var _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-three/fiber */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/RGBELoader.js\");\n/* harmony import */ var three_stdlib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! three-stdlib */ \"(ssr)/./node_modules/three-stdlib/loaders/EXRLoader.js\");\n/* harmony import */ var _monogrid_gainmap_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @monogrid/gainmap-js */ \"(ssr)/./node_modules/@monogrid/gainmap-js/dist/decode.js\");\n/* harmony import */ var _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../helpers/environment-assets.js */ \"(ssr)/./node_modules/@react-three/drei/helpers/environment-assets.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n\n\n\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  colorSpace = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    validatePreset(preset);\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  const multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.A)(state => state.gl);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F.clear(loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = (0,_react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F)(loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      // @ts-expect-error\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? three__WEBPACK_IMPORTED_MODULE_3__.CubeReflectionMapping : three__WEBPACK_IMPORTED_MODULE_3__.EquirectangularReflectionMapping;\n  texture.colorSpace = colorSpace !== null && colorSpace !== void 0 ? colorSpace : isCubemap ? 'srgb' : 'srgb-linear';\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F.preload(loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  _react_three_fiber__WEBPACK_IMPORTED_MODULE_2__.F.clear(loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in _helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(_helpers_environment_assets_js__WEBPACK_IMPORTED_MODULE_1__.presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? three__WEBPACK_IMPORTED_MODULE_3__.CubeTextureLoader : extension === 'hdr' ? three_stdlib__WEBPACK_IMPORTED_MODULE_4__.RGBELoader : extension === 'exr' ? three_stdlib__WEBPACK_IMPORTED_MODULE_5__.EXRLoader : extension === 'jpg' || extension === 'jpeg' ? _monogrid_gainmap_js__WEBPACK_IMPORTED_MODULE_6__.HDRJPGLoader : extension === 'webp' ? _monogrid_gainmap_js__WEBPACK_IMPORTED_MODULE_6__.GainMapLoader : null;\n  return loader;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/core/useEnvironment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/drei/helpers/environment-assets.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@react-three/drei/helpers/environment-assets.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   presetsObj: () => (/* binding */ presetsObj)\n/* harmony export */ });\nconst presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2RyZWkvaGVscGVycy9lbnZpcm9ubWVudC1hc3NldHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ29sZF9cXHBqc1xcaTUtZDMtd2FycmFudHlhaVxcbm9kZV9tb2R1bGVzXFxAcmVhY3QtdGhyZWVcXGRyZWlcXGhlbHBlcnNcXGVudmlyb25tZW50LWFzc2V0cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBwcmVzZXRzT2JqID0ge1xuICBhcGFydG1lbnQ6ICdsZWJvbWJvXzFrLmhkcicsXG4gIGNpdHk6ICdwb3RzZGFtZXJfcGxhdHpfMWsuaGRyJyxcbiAgZGF3bjogJ2tpYXJhXzFfZGF3bl8xay5oZHInLFxuICBmb3Jlc3Q6ICdmb3Jlc3Rfc2xvcGVfMWsuaGRyJyxcbiAgbG9iYnk6ICdzdF9mYWdhbnNfaW50ZXJpb3JfMWsuaGRyJyxcbiAgbmlnaHQ6ICdkaWtob2xvbG9fbmlnaHRfMWsuaGRyJyxcbiAgcGFyazogJ3Jvb2l0b3VfcGFya18xay5oZHInLFxuICBzdHVkaW86ICdzdHVkaW9fc21hbGxfMDNfMWsuaGRyJyxcbiAgc3Vuc2V0OiAndmVuaWNlX3N1bnNldF8xay5oZHInLFxuICB3YXJlaG91c2U6ICdlbXB0eV93YXJlaG91c2VfMDFfMWsuaGRyJ1xufTtcblxuZXhwb3J0IHsgcHJlc2V0c09iaiB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/drei/helpers/environment-assets.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ useThree),\n/* harmony export */   B: () => (/* binding */ Block),\n/* harmony export */   C: () => (/* binding */ useFrame),\n/* harmony export */   D: () => (/* binding */ useGraph),\n/* harmony export */   E: () => (/* binding */ ErrorBoundary),\n/* harmony export */   F: () => (/* binding */ useLoader),\n/* harmony export */   _: () => (/* binding */ _roots),\n/* harmony export */   a: () => (/* binding */ useMutableCallback),\n/* harmony export */   b: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   c: () => (/* binding */ createRoot),\n/* harmony export */   d: () => (/* binding */ unmountComponentAtNode),\n/* harmony export */   e: () => (/* binding */ extend),\n/* harmony export */   f: () => (/* binding */ createPointerEvents),\n/* harmony export */   g: () => (/* binding */ createEvents),\n/* harmony export */   h: () => (/* binding */ flushGlobalEffects),\n/* harmony export */   i: () => (/* binding */ isRef),\n/* harmony export */   j: () => (/* binding */ addEffect),\n/* harmony export */   k: () => (/* binding */ addAfterEffect),\n/* harmony export */   l: () => (/* binding */ addTail),\n/* harmony export */   m: () => (/* binding */ invalidate),\n/* harmony export */   n: () => (/* binding */ advance),\n/* harmony export */   o: () => (/* binding */ createPortal),\n/* harmony export */   p: () => (/* binding */ context),\n/* harmony export */   q: () => (/* binding */ applyProps),\n/* harmony export */   r: () => (/* binding */ reconciler),\n/* harmony export */   s: () => (/* binding */ getRootState),\n/* harmony export */   t: () => (/* binding */ threeTypes),\n/* harmony export */   u: () => (/* binding */ useBridge),\n/* harmony export */   v: () => (/* binding */ dispose),\n/* harmony export */   w: () => (/* binding */ act),\n/* harmony export */   x: () => (/* binding */ buildGraph),\n/* harmony export */   y: () => (/* binding */ useInstanceHandle),\n/* harmony export */   z: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.core.js\");\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var zustand_traditional__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! zustand/traditional */ \"(ssr)/./node_modules/zustand/esm/traditional.mjs\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n/* harmony import */ var suspend_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! suspend-react */ \"(ssr)/./node_modules/suspend-react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n\n\n\n\n\n\n\n\n\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n/**\r\n * Returns the instance's initial (outmost) root.\r\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n/**\r\n * Safely flush async effects when testing, simulating a legacy root.\r\n * @deprecated Import from React instead. import { act } from 'react'\r\n */\n// Reference with computed key to break Webpack static analysis\n// https://github.com/webpack/webpack/issues/14814\nconst act = react__WEBPACK_IMPORTED_MODULE_0__['act' + ''];\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\nconst isColorRepresentation = value => value != null && (typeof value === 'string' || typeof value === 'number' || value.isColor);\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = /* @__PURE__ */((_window$document, _window$navigator) => typeof window !== 'undefined' && (((_window$document = window.document) == null ? void 0 : _window$document.createElement) || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative'))() ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\r\n * Bridges renderer Context and StrictMode from a primary renderer.\r\n */\nfunction useBridge() {\n  const fiber = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useFiber)();\n  const ContextBridge = (0,its_fine__WEBPACK_IMPORTED_MODULE_5__.useContextBridge)();\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!(0,its_fine__WEBPACK_IMPORTED_MODULE_5__.traverseFiber)(fiber, true, node => node.type === react__WEBPACK_IMPORTED_MODULE_0__.StrictMode);\n    const Root = strict ? react__WEBPACK_IMPORTED_MODULE_0__.StrictMode : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Root, {\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\n\n// NOTE: static members get down-level transpiled to mutations which break tree-shaking\nconst ErrorBoundary = /* @__PURE__ */(_ErrorBoundary => (_ErrorBoundary = class ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}, _ErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n}), _ErrorBoundary))();\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  nul: a => a === null,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {},\n    meshes: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n      if (obj.isMesh && !data.meshes[obj.name]) data.meshes[obj.name] = obj;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) object.__r3f = instance;\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  target = root;\n  for (const part of key.split('-')) {\n    var _target;\n    key = part;\n    root = target;\n    target = (_target = target) == null ? void 0 : _target[key];\n  }\n\n  // TODO: change key to 'foo-bar' if target is undefined?\n\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\nfunction getMemoizedPrototype(root) {\n  let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n  try {\n    if (!ctor) {\n      ctor = new root.constructor();\n      MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n    }\n  } catch (e) {\n    // ...\n  }\n  return ctor;\n}\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      const ctor = getMemoizedPrototype(root);\n      if (!is.und(ctor)) changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenColorMap', 'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  var _instance$object;\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n      continue;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Layers must be written to the mask property\n    if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers && value instanceof three__WEBPACK_IMPORTED_MODULE_6__.Layers) {\n      target.mask = value.mask;\n    }\n    // Set colors if valid color representation for automatic conversion (copy)\n    else if (target instanceof three__WEBPACK_IMPORTED_MODULE_6__.Color && isColorRepresentation(value)) {\n      target.set(value);\n    }\n    // Copy if properties match signatures and implement math interface (likely read-only)\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof target.copy === 'function' && value != null && value.constructor && target.constructor === value.constructor) {\n      target.copy(value);\n    }\n    // Set array types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && Array.isArray(value)) {\n      if (typeof target.fromArray === 'function') target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target !== null && typeof target === 'object' && typeof target.set === 'function' && typeof value === 'number') {\n      // Allow setting array scalars\n      if (typeof target.setScalar === 'function') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n    }\n    // Else, just overwrite the value\n    else {\n      var _root$key;\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && (_root$key = root[key]) != null && _root$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === three__WEBPACK_IMPORTED_MODULE_6__.RGBAFormat && root[key].type === three__WEBPACK_IMPORTED_MODULE_6__.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        root[key].colorSpace = three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && (_instance$object = instance.object) != null && _instance$object.isObject3D && prevHandlers !== instance.eventCount) {\n    const object = instance.object;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && object.raycast !== null) {\n      rootState.internal.interaction.push(object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object.isBufferGeometry) instance.props.attach = 'geometry';else if (instance.object.isMaterial) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        let state = getRootState(hit.object);\n\n        // If the object is not managed by R3F, it might be parented to an element which is.\n        // Traverse upwards until we find a managed parent and use its state instead.\n        if (!state) {\n          hit.object.traverseAncestors(obj => {\n            const parentState = getRootState(obj);\n            if (parentState) {\n              state = parentState;\n              return false;\n            }\n          });\n        }\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get) => {\n    const position = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n    const defaultTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n    const tempTarget = new three__WEBPACK_IMPORTED_MODULE_6__.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new three__WEBPACK_IMPORTED_MODULE_6__.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new three__WEBPACK_IMPORTED_MODULE_6__.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      if (viewport.dpr > 0) gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\r\n * Exposes an object's {@link Instance}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\r\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\r\n */\nfunction useStore() {\n  const store = react__WEBPACK_IMPORTED_MODULE_0__.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor$1 = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor$1(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (isObject3D(data == null ? void 0 : data.scene)) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.suspend)(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.preload)(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return (0,suspend_react__WEBPACK_IMPORTED_MODULE_8__.clear)([loader, ...keys]);\n};\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nfunction createReconciler(config) {\n  const reconciler = react_reconciler__WEBPACK_IMPORTED_MODULE_2___default()(config);\n  reconciler.injectIntoDevTools({\n    bundleType: typeof process !== 'undefined' && \"development\" !== 'production' ? 1 : 0,\n    rendererPackageName: '@react-three/fiber',\n    version: react__WEBPACK_IMPORTED_MODULE_0__.version\n  });\n  return reconciler;\n}\nconst NoEventPriority = 0;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nconst PREFIX_REGEX = /^three(?=[A-Z])/;\nconst toPascalCase = type => `${type[0].toUpperCase()}${type.slice(1)}`;\nlet i = 0;\nconst isConstructor = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = toPascalCase(type);\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  // Remove three* prefix from elements if native element not present\n  type = toPascalCase(type) in catalogue ? type : type.replace(PREFIX_REGEX, '');\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const target = catalogue[toPascalCase(child.type)];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n  }\n\n  // Set initial props\n  applyProps(child.object, child.props);\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      // If the child is already in the parent's children array, move it to the new position\n      // Otherwise, just insert it at the target position\n      const existingIndex = parent.object.children.indexOf(child.object);\n      if (existingIndex !== -1) {\n        parent.object.children.splice(existingIndex, 1);\n        const adjustedIndex = existingIndex < childIndex ? childIndex - 1 : childIndex;\n        parent.object.children.splice(adjustedIndex, 0, child.object);\n      } else {\n        child.object.parent = parent.object;\n        parent.object.children.splice(childIndex, 0, child.object);\n        child.object.dispatchEvent({\n          type: 'added'\n        });\n        parent.object.dispatchEvent({\n          type: 'childadded',\n          child: child.object\n        });\n      }\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else (0,scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_scheduleCallback)(scheduler__WEBPACK_IMPORTED_MODULE_3__.unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const target = catalogue[toPascalCase(instance.type)];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, make it no-op\nconst handleTextInstance = () => {};\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = /* @__PURE__ */createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition: () => false,\n  trackSchedulerEvent: () => {},\n  resolveEventType: () => null,\n  resolveEventTimeStamp: () => -1.1,\n  requestPostPaintCallback() {},\n  maySuspendCommit: () => false,\n  preloadInstance: () => true,\n  // true indicates already loaded\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady: () => null,\n  NotPendingTransition: null,\n  HostTransitionContext: /* @__PURE__ */react__WEBPACK_IMPORTED_MODULE_0__.createContext(null),\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ContinuousEventPriority;\n      default:\n        return react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\n\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  react_reconciler_constants__WEBPACK_IMPORTED_MODULE_1__.ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let lastCamera;\n  let configured = false;\n  let pending = null;\n  return {\n    async configure(props = {}) {\n      let resolve;\n      pending = new Promise(_resolve => resolve = _resolve);\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) {\n        const defaultProps = {\n          canvas: canvas,\n          powerPreference: 'high-performance',\n          antialias: true,\n          alpha: true\n        };\n        const customRenderer = typeof glConfig === 'function' ? await glConfig(defaultProps) : glConfig;\n        if (isRenderer(customRenderer)) {\n          gl = customRenderer;\n        } else {\n          gl = new three__WEBPACK_IMPORTED_MODULE_9__.WebGLRenderer({\n            ...defaultProps,\n            ...glConfig\n          });\n        }\n        state.set({\n          gl\n        });\n      }\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions == null ? void 0 : cameraOptions.isCamera;\n        const camera = isCamera ? cameraOptions : orthographic ? new three__WEBPACK_IMPORTED_MODULE_6__.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new three__WEBPACK_IMPORTED_MODULE_6__.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new three__WEBPACK_IMPORTED_MODULE_6__.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: three__WEBPACK_IMPORTED_MODULE_6__.BasicShadowMap,\n            percentage: three__WEBPACK_IMPORTED_MODULE_6__.PCFShadowMap,\n            soft: three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap,\n            variance: three__WEBPACK_IMPORTED_MODULE_6__.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : three__WEBPACK_IMPORTED_MODULE_6__.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n      three__WEBPACK_IMPORTED_MODULE_6__.ColorManagement.enabled = !legacy;\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        gl.outputColorSpace = linear ? three__WEBPACK_IMPORTED_MODULE_6__.LinearSRGBColorSpace : three__WEBPACK_IMPORTED_MODULE_6__.SRGBColorSpace;\n        gl.toneMapping = flat ? three__WEBPACK_IMPORTED_MODULE_6__.NoToneMapping : three__WEBPACK_IMPORTED_MODULE_6__.ACESFilmicToneMapping;\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      resolve();\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured && !pending) this.configure();\n      pending.then(() => {\n        reconciler.updateContainer( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Provider, {\n          store: store,\n          children: children,\n          onCreated: onCreated,\n          rootElement: canvas\n        }), fiber, null, () => undefined);\n      });\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_6__.Raycaster());\n  const [pointer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(() => new three__WEBPACK_IMPORTED_MODULE_6__.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new three__WEBPACK_IMPORTED_MODULE_6__.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = (0,zustand_traditional__WEBPACK_IMPORTED_MODULE_7__.createWithEqualityFn)((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return (\n    /*#__PURE__*/\n    // @ts-ignore, reconciler types are not maintained\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n      children: reconciler.createPortal( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(context.Provider, {\n        value: usePortalStore,\n        children: children\n      }), usePortalStore, null)\n    })\n  );\n}\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = false;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\r\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\r\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      if (events.handlers) {\n        for (const name in events.handlers) {\n          const event = events.handlers[name];\n          const [eventName, passive] = DOM_EVENTS[name];\n          target.addEventListener(eventName, event, {\n            passive\n          });\n        }\n      }\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        if (events.handlers) {\n          for (const name in events.handlers) {\n            const event = events.handlers[name];\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        }\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Canvas: () => (/* binding */ Canvas),\n/* harmony export */   ReactThreeFiber: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   _roots: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__._),\n/* harmony export */   act: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   addAfterEffect: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   addEffect: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   addTail: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   advance: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   applyProps: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   buildGraph: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   context: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   createEvents: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   createPortal: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   createRoot: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   dispose: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   events: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   extend: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   flushGlobalEffects: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   getRootState: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   invalidate: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   reconciler: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   unmountComponentAtNode: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   useFrame: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.C),\n/* harmony export */   useGraph: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.D),\n/* harmony export */   useInstanceHandle: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   useLoader: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.F),\n/* harmony export */   useStore: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   useThree: () => (/* reexport safe */ _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.A)\n/* harmony export */ });\n/* harmony import */ var _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events-dc44c1b8.esm.js */ \"(ssr)/./node_modules/@react-three/fiber/dist/events-dc44c1b8.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var three__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! three */ \"(ssr)/./node_modules/three/build/three.module.js\");\n/* harmony import */ var react_use_measure__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-use-measure */ \"(ssr)/./node_modules/react-use-measure/dist/index.js\");\n/* harmony import */ var its_fine__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! its-fine */ \"(ssr)/./node_modules/its-fine/dist/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_reconciler_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-reconciler/constants */ \"(ssr)/./node_modules/react-reconciler/constants.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-reconciler */ \"(ssr)/./node_modules/react-reconciler/index.js\");\n/* harmony import */ var react_reconciler__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_reconciler__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CanvasImpl({\n  ref,\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = _events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.f,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.e)(three__WEBPACK_IMPORTED_MODULE_6__), []);\n  const Bridge = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.u)();\n  const [containerRef, containerRect] = (0,react_use_measure__WEBPACK_IMPORTED_MODULE_7__[\"default\"])({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  const divRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle(ref, () => canvasRef.current);\n  const handlePointerMissed = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.a)(onPointerMissed);\n  const [block, setBlock] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n  const [error, setError] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n  (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.b)(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.c)(canvas);\n      async function run() {\n        await root.current.configure({\n          gl,\n          scene,\n          events,\n          shadows,\n          linear,\n          flat,\n          legacy,\n          orthographic,\n          frameloop,\n          dpr,\n          performance,\n          raycaster,\n          camera,\n          size: containerRect,\n          // Pass mutable reference to onPointerMissed so it's free to update\n          onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n          onCreated: state => {\n            // Connect to event source\n            state.events.connect == null ? void 0 : state.events.connect(eventSource ? (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.i)(eventSource) ? eventSource.current : eventSource : divRef.current);\n            // Set up compute function\n            if (eventPrefix) {\n              state.setEvents({\n                compute: (event, state) => {\n                  const x = event[eventPrefix + 'X'];\n                  const y = event[eventPrefix + 'Y'];\n                  state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                  state.raycaster.setFromCamera(state.pointer, state.camera);\n                }\n              });\n            }\n            // Call onCreated callback\n            onCreated == null ? void 0 : onCreated(state);\n          }\n        });\n        root.current.render( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Bridge, {\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.E, {\n            set: setError,\n            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n              fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.B, {\n                set: setBlock\n              }),\n              children: children != null ? children : null\n            })\n          })\n        }));\n      }\n      run();\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => (0,_events_dc44c1b8_esm_js__WEBPACK_IMPORTED_MODULE_0__.d)(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n}\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nfunction Canvas(props) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(its_fine__WEBPACK_IMPORTED_MODULE_8__.FiberProvider, {\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(CanvasImpl, {\n      ...props\n    })\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/dist/react-three-fiber.esm.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@react-three/fiber/node_modules/scheduler/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/scheduler.development.js */ \"(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/cjs/scheduler.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJlYWN0LXRocmVlL2ZpYmVyL25vZGVfbW9kdWxlcy9zY2hlZHVsZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsSUFBSSxLQUFxQyxFQUFFLEVBRTFDLENBQUM7QUFDRixFQUFFLHlLQUEwRDtBQUM1RCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxnb2xkX1xccGpzXFxpNS1kMy13YXJyYW50eWFpXFxub2RlX21vZHVsZXNcXEByZWFjdC10aHJlZVxcZmliZXJcXG5vZGVfbW9kdWxlc1xcc2NoZWR1bGVyXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvc2NoZWR1bGVyLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvc2NoZWR1bGVyLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@react-three/fiber/node_modules/scheduler/index.js\n");

/***/ })

};
;