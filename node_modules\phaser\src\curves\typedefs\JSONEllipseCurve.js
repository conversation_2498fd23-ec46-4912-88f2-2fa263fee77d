/**
 * @typedef {object} Phaser.Types.Curves.JSONEllipseCurve
 * @since 3.0.0
 *
 * @property {string} type - The of the curve.
 * @property {number} x - The x coordinate of the ellipse.
 * @property {number} y - The y coordinate of the ellipse.
 * @property {number} xRadius - The horizontal radius of ellipse.
 * @property {number} yRadius - The vertical radius of ellipse.
 * @property {number} startAngle - The start angle of the ellipse, in degrees.
 * @property {number} endAngle - The end angle of the ellipse, in degrees.
 * @property {boolean} clockwise - Sets if the the ellipse rotation is clockwise (true) or anti-clockwise (false)
 * @property {number} rotation - The rotation of ellipse, in degrees.
 */
