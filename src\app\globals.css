@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* WarrantyAI Design System - Futuristic AI Theme */
:root {
  /* Primary Colors - AI Inspired */
  --primary-blue: #0066FF;
  --primary-cyan: #00FFFF;
  --primary-purple: #6366F1;
  --primary-pink: #EC4899;

  /* Accent Colors */
  --accent-green: #10B981;
  --accent-orange: #F59E0B;
  --accent-red: #EF4444;

  /* Neutral Colors - Dark Theme */
  --neutral-900: #0F0F23;
  --neutral-800: #1A1A2E;
  --neutral-700: #16213E;
  --neutral-600: #0F3460;
  --neutral-500: #533A7B;
  --neutral-400: #9CA3AF;
  --neutral-300: #D1D5DB;
  --neutral-200: #E5E7EB;
  --neutral-100: #F3F4F6;
  --neutral-50: #F9FAFB;

  /* Gradient Combinations */
  --gradient-primary: linear-gradient(135deg, #0066FF, #00FFFF);
  --gradient-secondary: linear-gradient(135deg, #6366F1, #EC4899);
  --gradient-accent: linear-gradient(135deg, #10B981, #F59E0B);
  --gradient-dark: linear-gradient(135deg, #0F0F23, #1A1A2E);

  /* Background & Foreground */
  --background: var(--neutral-900);
  --foreground: var(--neutral-50);

  /* Font Families */
  --font-primary: 'Inter', system-ui, sans-serif;
  --font-display: 'Space Grotesk', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;

  /* Animation Durations */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
  --duration-slower: 0.75s;

  /* Shadows & Glows */
  --shadow-glow-blue: 0 0 20px rgba(0, 102, 255, 0.3);
  --shadow-glow-cyan: 0 0 20px rgba(0, 255, 255, 0.3);
  --shadow-glow-purple: 0 0 20px rgba(99, 102, 241, 0.3);
  --shadow-glow-pink: 0 0 20px rgba(236, 72, 153, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-primary);
  --font-display: var(--font-display);
  --font-mono: var(--font-mono);

  /* Custom Tailwind Colors */
  --color-primary-blue: var(--primary-blue);
  --color-primary-cyan: var(--primary-cyan);
  --color-primary-purple: var(--primary-purple);
  --color-primary-pink: var(--primary-pink);
  --color-accent-green: var(--accent-green);
  --color-accent-orange: var(--accent-orange);
  --color-accent-red: var(--accent-red);
  --color-neutral-900: var(--neutral-900);
  --color-neutral-800: var(--neutral-800);
  --color-neutral-700: var(--neutral-700);
  --color-neutral-600: var(--neutral-600);
  --color-neutral-500: var(--neutral-500);
  --color-neutral-400: var(--neutral-400);
  --color-neutral-300: var(--neutral-300);
  --color-neutral-200: var(--neutral-200);
  --color-neutral-100: var(--neutral-100);
  --color-neutral-50: var(--neutral-50);
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-primary);
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-800);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-cyan);
}

/* Selection Styles */
::selection {
  background: var(--primary-blue);
  color: var(--neutral-50);
}

::-moz-selection {
  background: var(--primary-blue);
  color: var(--neutral-50);
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px currentColor; }
  50% { box-shadow: 0 0 20px currentColor, 0 0 30px currentColor; }
}

@keyframes matrix {
  0% { opacity: 0; transform: translateY(-100%); }
  50% { opacity: 1; }
  100% { opacity: 0; transform: translateY(100%); }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* Utility Classes */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-bg {
  background: var(--gradient-primary);
}

.gradient-bg-secondary {
  background: var(--gradient-secondary);
}

.gradient-bg-accent {
  background: var(--gradient-accent);
}

.glow-blue {
  box-shadow: var(--shadow-glow-blue);
}

.glow-cyan {
  box-shadow: var(--shadow-glow-cyan);
}

.glow-purple {
  box-shadow: var(--shadow-glow-purple);
}

.glow-pink {
  box-shadow: var(--shadow-glow-pink);
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-matrix {
  animation: matrix 2s linear infinite;
}

.animate-typing {
  animation: typing 3s steps(40, end), blink 0.75s step-end infinite;
  white-space: nowrap;
  overflow: hidden;
  border-right: 2px solid currentColor;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-rotate-in {
  animation: rotateIn 0.6s ease-out;
}

/* Glass Morphism */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Neon Effects */
.neon-blue {
  color: var(--primary-blue);
  text-shadow: 0 0 5px var(--primary-blue), 0 0 10px var(--primary-blue), 0 0 15px var(--primary-blue);
}

.neon-cyan {
  color: var(--primary-cyan);
  text-shadow: 0 0 5px var(--primary-cyan), 0 0 10px var(--primary-cyan), 0 0 15px var(--primary-cyan);
}

.neon-purple {
  color: var(--primary-purple);
  text-shadow: 0 0 5px var(--primary-purple), 0 0 10px var(--primary-purple), 0 0 15px var(--primary-purple);
}

.neon-pink {
  color: var(--primary-pink);
  text-shadow: 0 0 5px var(--primary-pink), 0 0 10px var(--primary-pink), 0 0 15px var(--primary-pink);
}
