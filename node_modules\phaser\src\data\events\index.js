/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * @namespace Phaser.Data.Events
 */

module.exports = {

    CHANGE_DATA: require('./CHANGE_DATA_EVENT'),
    CHANGE_DATA_KEY: require('./CHANGE_DATA_KEY_EVENT'),
    DESTROY: require('./DESTROY_EVENT'),
    REMOVE_DATA: require('./REMOVE_DATA_EVENT'),
    SET_DATA: require('./SET_DATA_EVENT')

};
