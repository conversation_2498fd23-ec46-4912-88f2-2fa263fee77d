/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * Phaser Scale Manager constants for orientation.
 *
 * @namespace Phaser.Scale.Orientation
 * @memberof Phaser.Scale
 * @since 3.16.0
 */

/**
 * Phaser Scale Manager constants for orientation.
 *
 * To find out what each mode does please see [Phaser.Scale.Orientation]{@link Phaser.Scale.Orientation}.
 *
 * @typedef {Phaser.Scale.Orientation} Phaser.Scale.OrientationType
 * @memberof Phaser.Scale
 * @since 3.16.0
 */

module.exports = {

    /**
     * The primary landscape orientation.
     *
     * @name Phaser.Scale.Orientation.LANDSCAPE
     * @type {string}
     * @const
     * @since 3.16.0
     */
    LANDSCAPE: 'landscape-primary',

    /**
     * The secondary landscape orientation.
     *
     * @name Phaser.Scale.Orientation.LANDSCAPE_SECONDARY
     * @type {string}
     * @const
     * @since 3.85.0
     */
    LANDSCAPE_SECONDARY: 'landscape-secondary',

    /**
     * The primary portrait orientation.
     *
     * @name Phaser.Scale.Orientation.PORTRAIT
     * @type {string}
     * @const
     * @since 3.16.0
     */
    PORTRAIT: 'portrait-primary',

    /**
     * The secondary portrait orientation.
     *
     * @name Phaser.Scale.Orientation.PORTRAIT_SECONDARY
     * @type {string}
     * @const
     * @since 3.16.0
     */
    PORTRAIT_SECONDARY: 'portrait-secondary'

};
