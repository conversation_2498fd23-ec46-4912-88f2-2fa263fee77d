/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Scale Manager Orientation Change Event.
 *
 * This event is dispatched whenever the Scale Manager detects an orientation change event from the browser.
 *
 * @event Phaser.Scale.Events#ORIENTATION_CHANGE
 * @type {string}
 * @since 3.16.1
 *
 * @param {string} orientation - The new orientation value. Either `Phaser.Scale.Orientation.LANDSCAPE` or `Phaser.Scale.Orientation.PORTRAIT`.
 */
module.exports = 'orientationchange';
